import { db } from '@/lib/prisma';
import type { Prisma } from '@/lib/generated/prisma/client';
import { auth } from '@/auth';
import { headers } from 'next/headers';

export type DiscoverSort = 'trending' | 'active' | 'new' | 'upvoted';

export type DiscoverParams = {
  q?: string;
  tags?: string[];
  features?: { verified?: boolean; partnered?: boolean; nsfw?: boolean };
  language?: string;
  region?: string;
  activity?: ('LOW' | 'MEDIUM' | 'HIGH')[];
  sort?: DiscoverSort;
  page?: number;
  pageSize?: number;
};

export type HubCardDTO = {
  id: string;
  name: string;
  description: string | null;
  iconUrl: string | null;
  bannerUrl: string | null;
  weeklyMessageCount: number;
  activityLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  language: string | null;
  region: string | null;
  verified: boolean;
  partnered: boolean;
  nsfw: boolean;
  tags: { name: string; color: string | null }[];
  activityMetrics: { messagesLast24h: number; activeUsersLast24h: number } | null;
  _count: { upvotes: number; connections: number; messages: number };
  averageRating?: number | null;
  isUpvoted?: boolean; // Add this to track user upvote status
};

async function getBaseSelect(userId?: string) {
  const baseSelect = {
    id: true,
    name: true,
    description: true,
    iconUrl: true,
    bannerUrl: true,
    weeklyMessageCount: true,
    activityLevel: true,
    language: true,
    region: true,
    verified: true,
    partnered: true,
    nsfw: true,
    tags: { select: { name: true, color: true } },
    activityMetrics: { select: { messagesLast24h: true, activeUsersLast24h: true } },
    _count: {
      select: { upvotes: true, messages: true, connections: { where: { connected: true } } },
    },
  } as const;

  // Add upvotes relation if user is authenticated
  if (userId) {
    return {
      ...baseSelect,
      upvotes: {
        where: { userId },
        select: { userId: true },
        take: 1,
      },
    };
  }

  return baseSelect;
}

function buildWhere(p: DiscoverParams): Prisma.HubWhereInput {
  const and: Prisma.HubWhereInput[] = [{ private: false }];

  if (p.q) {
    and.push({
      OR: [
        { name: { contains: p.q, mode: 'insensitive' } },
        { description: { contains: p.q, mode: 'insensitive' } },
      ],
    });
  }

  if (p.language) and.push({ language: p.language });
  if (p.region) and.push({ region: p.region });
  if (p.activity?.length) and.push({ activityLevel: { in: p.activity } });

  if (p.features?.verified) and.push({ verified: true });
  if (p.features?.partnered) and.push({ partnered: true });

  // NSFW: only include if explicitly requested
  if (!p.features?.nsfw) and.push({ nsfw: false });

  if (p.tags?.length) {
    for (const t of p.tags) {
      and.push({ tags: { some: { name: t } } });
    }
  }

  return { AND: and };
}

function buildOrderBy(sort: DiscoverSort | undefined): Prisma.HubOrderByWithRelationInput[] {
  switch (sort) {
    case 'active':
      return [{ weeklyMessageCount: 'desc' }, { id: 'desc' }];
    case 'new':
      return [{ createdAt: 'desc' }, { id: 'desc' }];
    case 'upvoted':
      return [{ upvotes: { _count: 'desc' } }, { id: 'desc' }];
    case 'trending':
    default:
      return [
        { activityMetrics: { messagesLast24h: 'desc' } },
        { weeklyMessageCount: 'desc' },
        { upvotes: { _count: 'desc' } },
        { id: 'desc' },
      ];
  }
}

export async function getDiscoverHubs(params: DiscoverParams) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;

  const page = Math.max(1, params.page ?? 1);
  const pageSize = Math.min(60, Math.max(1, params.pageSize ?? 24));
  const where = buildWhere(params);
  const orderBy = buildOrderBy(params.sort);
  const select = await getBaseSelect(userId);

  const [items] = await Promise.all([
    db.hub.findMany({
      where,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
      select,
    }),
  ]);

  return {
    items: items.map((item) => {
      const typedItem = item as { upvotes?: { userId: string }[] };
      return {
        ...item,
        weeklyMessageCount: item._count.messages,
        isUpvoted: userId ? (typedItem.upvotes?.length || 0) > 0 : false,
      };
    }) as unknown as HubCardDTO[],
    page,
    pageSize,
    total: items.length,
    nextPage: page * pageSize < items.length ? page + 1 : null,
  } as const;
}
