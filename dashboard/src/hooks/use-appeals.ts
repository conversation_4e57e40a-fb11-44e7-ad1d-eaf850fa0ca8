'use client';;
import { useErrorNotification } from './use-error-notification';
import { Appeal, Hub, Infraction, AppealStatus } from '@/lib/generated/prisma/client';
import { useToast } from '@/components/ui/use-toast';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

// Types
export interface AppealWithInfraction extends Appeal {
  infraction: Infraction & { hub: Hub };
}

export interface AppealsResponse {
  appeals: AppealWithInfraction[];
  total: number;
  page?: number;
  limit?: number;
  totalPages?: number;
}

// Query keys
export const appealKeys = {
  all: ['appeals'] as const,
  lists: () => [...appealKeys.all, 'list'] as const,
  list: (filters: { myAppeals?: boolean; page?: number; limit?: number }) =>
    [...appealKeys.lists(), filters] as const,
  details: () => [...appealKeys.all, 'detail'] as const,
  detail: (id: string) => [...appealKeys.details(), id] as const,
};

// Fetch appeals
// No top-level fetcher; tRPC hooks used directly

// Hook for fetching appeals
export function useAppeals(params: {
  myAppeals?: boolean;
  page?: number;
  limit?: number;
  hubId?: string;
  status?: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  userId?: string;
  infractionId?: string;
}) {
  const trpc = useTRPC();
  const query = useQuery(trpc.appeal.list.queryOptions({
    myAppeals: params.myAppeals,
    page: params.page,
    limit: params.limit,
    hubId: params.hubId,
    status: params.status as AppealStatus | undefined,
    userId: params.userId,
    infractionId: params.infractionId,
  }));

  // Handle error notification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to load appeals',
  });

  return query;
}

// Hook for fetching my appeals
export function useMyAppeals(page: number = 1, limit: number = 10) {
  return useAppeals({ myAppeals: true, page, limit });
}

// Mutation for updating appeal status
export function useUpdateAppealStatus() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.appeal.updateStatus.mutationOptions({
    onError: () => {
      toast({
        title: 'Error',
        description: 'Failed to update appeal status.',
        variant: 'destructive',
      });
    },
    onSettled: async () => {
      await queryClient.invalidateQueries(trpc.appeal.list.pathFilter());
    },
  }));
}
