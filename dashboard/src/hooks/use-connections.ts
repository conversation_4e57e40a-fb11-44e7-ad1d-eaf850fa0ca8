'use client';;
import { BasicHubConnection, ServerData } from '@/app/dashboard/hubs/[hubId]/connections/client';
import { useToast } from '@/components/ui/use-toast';
import { useErrorNotification } from './use-error-notification';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

// Query keys
export const connectionKeys = {
  all: ['connections'] as const,
  lists: () => [...connectionKeys.all, 'list'] as const,
  list: (hubId: string) => ['connections', hubId] as const, // Simplified to match server prefetching
  details: () => [...connectionKeys.all, 'detail'] as const,
  detail: (id: string) => [...connectionKeys.details(), id] as const,
};

// Hook for fetching connections
export function useConnections(hubId: string) {
  const trpc = useTRPC();
  const query = useQuery(trpc.connection.listByHub.queryOptions(
    { hubId },
    {
      staleTime: 60 * 1000,
      gcTime: 5 * 60 * 1000,
      select: (res) =>
        [...(res.connections as (BasicHubConnection & { server: ServerData | null })[])].sort(
          (a, b) => new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime(),
        ),
    },
  ));

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to load connections',
  });

  return query;
}

// Hook for removing a connection
export function useRemoveConnection(hubId?: string) {
  const trpc = useTRPC();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const mutation = useMutation(trpc.connection.remove.mutationOptions({
    onMutate: async (vars) => {
      // Cancel and snapshot current data
      const previousConnections = hubId ? queryClient.getQueryData(trpc.connection.listByHub.queryKey({ hubId })) : undefined;
      if (hubId) await queryClient.cancelQueries(trpc.connection.listByHub.queryFilter({ hubId }));

      if (hubId) {
        queryClient.setQueryData(trpc.connection.listByHub.queryKey({ hubId }), (old) => {
          if (!old) return old;
          return {
            connections: old.connections.filter((c) => c.id !== vars.connectionId),
          };
        });
      }

      return { previousConnections };
    },
    onSuccess: () => {
      toast({
        description: 'Connection removed successfully',
      });
    },
    onError: (error, _, context) => {
      // Revert the optimistic update
      if (hubId && context?.previousConnections) {
        queryClient.setQueryData(trpc.connection.listByHub.queryKey({ hubId }), context.previousConnections);
      }

      toast({
        variant: 'destructive',
        title: 'Error',
        description: `Failed to remove connection: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    },
    onSettled: () => {
      // Invalidate and refetch using trpc utils
      if (hubId) queryClient.invalidateQueries(trpc.connection.listByHub.queryFilter({ hubId })).catch(() => {});
      else queryClient.invalidateQueries(trpc.connection.listByHub.pathFilter()).catch(() => {});
    },
  }));

  return {
    remove: (connectionId: string) => mutation.mutate({ connectionId, hubId }),
    ...mutation,
  } as const;
}
