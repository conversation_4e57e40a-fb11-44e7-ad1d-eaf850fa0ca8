'use client';;
import { useToast } from '@/components/ui/use-toast';
import { useErrorNotification } from './use-error-notification';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

// Hook for fetching a hub
export function useHub(hubId: string) {
  const trpc = useTRPC();
  const query = useQuery(trpc.hub.getHub.queryOptions({ id: hubId }));

  // Handle error with useEffect
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to load hub',
  });

  return query;
}

// Hook for upvoting a hub
export function useHubUpvote(hubId: string) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.hub.upvoteHub.mutationOptions({
    onSuccess: (data) => {
      toast({
        title: data.upvoted ? 'Upvoted hub' : 'Removed upvote',
        description: data.upvoted ? 'Thanks for your support!' : "You've removed your upvote",
        duration: 2000,
      });

      // Invalidate the hub query to refresh the data
      queryClient.invalidateQueries(trpc.hub.getHub.queryFilter({ id: hubId }));
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update upvote status: ${error.message}`,
        variant: 'destructive',
      });
    },
  }));
}
