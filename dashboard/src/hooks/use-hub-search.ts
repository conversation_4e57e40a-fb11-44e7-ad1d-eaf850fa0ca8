'use client';;
import { SortOptions } from '@/app/hubs/constants';
import { useErrorNotification } from './use-error-notification';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";

// Interface for search parameters
export interface HubSearchParams {
  search?: string;
  tags?: string[];
  sort?: SortOptions;
  skip?: number;
  limit?: number;
}

// Hook for searching hubs
export function useHubSearch(params: HubSearchParams) {
  const trpc = useTRPC();
  const query = useQuery(trpc.hub.getHubs.queryOptions(
    {
      search: params.search,
      tags: params.tags,
      sort: params.sort,
      skip: params.skip,
      limit: params.limit,
    },
    {
      // Disable automatic refetching when window is focused
      refetchOnWindowFocus: false,
    },
  ));

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to search hubs',
  });

  return query;
}
