'use client';;
import { useErrorNotification } from './use-error-notification';
import { useToast } from '@/components/ui/use-toast';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

// Types (match the tRPC router outputs)
export interface User {
  id: string;
  name: string | null;
  image: string | null;
}

export interface Moderator {
  id: string;
  userId: string;
  role: 'MODERATOR' | 'MANAGER';
  user: User;
}

export interface HubMembers {
  owner: User;
  moderators: Moderator[];
}

// Hook for fetching hub members
export function useHubMembers(hubId: string) {
  const trpc = useTRPC();
  const query = useQuery(trpc.hub.getMembers.queryOptions({ hubId }));

  // Handle error notification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to load hub members',
  });

  return query;
}

// Hook for adding a hub member
export function useAddHubMember(hubId: string) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.hub.addMember.mutationOptions({
    onSuccess: (data, variables) => {
      toast({
        title: 'Member Added',
        description: `User has been added as a ${variables.role.toLowerCase()}.`,
      });

      // Invalidate queries
      queryClient.invalidateQueries(trpc.hub.getMembers.queryFilter({ hubId }));
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  }));
}

// Hook for updating a member's role
export function useUpdateMemberRole(hubId: string) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.hub.updateMemberRole.mutationOptions({
    onSuccess: (data, variables) => {
      toast({
        title: 'Role Updated',
        description: `Member's role has been updated to ${variables.role.toLowerCase()}.`,
      });

      // Invalidate queries
      queryClient.invalidateQueries(trpc.hub.getMembers.queryFilter({ hubId }));
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  }));
}

// Hook for removing a member
export function useRemoveMember(hubId: string) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.hub.removeMember.mutationOptions({
    onSuccess: () => {
      toast({
        title: 'Member Removed',
        description: 'Member has been removed from the hub.',
      });

      // Invalidate queries
      queryClient.invalidateQueries(trpc.hub.getMembers.queryFilter({ hubId }));
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  }));
}
