"use client";;
import { useQueryClient } from "@/lib/tanstack-query";
import { useTRPC } from "@/utils/trpc";
import { useState } from "react";
import { useErrorNotification } from "./use-error-notification";

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";

// Query keys
export const notificationKeys = {
  all: ["notifications"] as const,
  lists: () => [...notificationKeys.all, "list"] as const,
  list: () => [...notificationKeys.lists()] as const,
};

// Hook for fetching notifications
export function useNotifications() {
  const trpc = useTRPC();
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch announcements
  const query = useQuery(trpc.announcement.getAnnouncements.queryOptions(undefined, {
    staleTime: 60 * 1000, // 1 minute stale time
    refetchOnWindowFocus: true,
  }));

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation(trpc.announcement.markAllAsRead.mutationOptions({
    onSuccess: () => {
      // Invalidate the announcements query to refetch with updated read status
      queryClient.invalidateQueries({ queryKey: notificationKeys.list() });
    },
  }));

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: "Error",
    description: "Failed to load notifications",
  });

  // Toggle the notification dropdown
  const toggleNotifications = () => {
    setIsOpen(!isOpen);
    
    // If opening the dropdown and there are unread notifications, mark them as read
    if (!isOpen && query.data?.unreadCount && query.data.unreadCount > 0) {
      markAllAsReadMutation.mutate();
    }
  };

  return {
    notifications: query.data?.announcements || [],
    unreadCount: query.data?.unreadCount || 0,
    isLoading: query.isLoading,
    isOpen,
    toggleNotifications,
    markAllAsRead: () => markAllAsReadMutation.mutate(),
  };
}
