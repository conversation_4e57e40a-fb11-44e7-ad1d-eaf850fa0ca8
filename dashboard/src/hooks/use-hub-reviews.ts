'use client';;
import { useToast } from '@/components/ui/use-toast';
import { useErrorNotification } from './use-error-notification';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

// Hook for fetching hub reviews
export function useHubReviews(hubId: string) {
  const trpc = useTRPC();
  const query = useQuery(trpc.hub.getHubReviews.queryOptions({ hubId }));

  // Handle error with useErrorNotification
  useErrorNotification({
    isError: query.isError,
    error: query.error,
    title: 'Error',
    description: 'Failed to load reviews',
  });

  return query;
}

// Hook for submitting a hub review via tRPC
export function useSubmitHubReview(hubId: string) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation(trpc.hub.createHubReview.mutationOptions({
    onSuccess: () => {
      toast({
        title: 'Review submitted',
        description: 'Thank you for your feedback!',
        duration: 2000,
      });
      queryClient.invalidateQueries(trpc.hub.getHubReviews.queryFilter({ hubId }));
      queryClient.invalidateQueries(trpc.hub.getHub.queryFilter({ id: hubId }));
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to submit review: ${error.message}`,
        variant: 'destructive',
      });
    },
  }));
}
