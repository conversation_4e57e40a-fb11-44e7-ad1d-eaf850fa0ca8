/**
 * Main tRPC router that combines all sub-routers
 */
import { router } from '../trpc';
import { hubRouter } from './hub';
import { userRouter } from './user';
import { serverRouter } from './server';
import { moderationRouter } from './moderation';
import { announcementRouter } from './announcement';
import { discoverRouter } from './discover';
import { tagsRouter } from './tags';
import { connectionRouter } from './connection';
import { appealRouter } from './appeal';

export const appRouter = router({
  hub: hubRouter,
  user: userRouter,
  server: serverRouter,
  moderation: moderationRouter,
  announcement: announcementRouter,
  discover: discoverRouter,
  tags: tagsRouter,
  connection: connectionRouter,
  appeal: appealRouter,
});

// Export type definition of API
export type AppRouter = typeof appRouter;
