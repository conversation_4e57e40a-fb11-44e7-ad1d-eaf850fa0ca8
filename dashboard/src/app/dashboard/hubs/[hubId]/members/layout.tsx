import { auth } from '@/auth';
import { PermissionLevel } from '@/lib/constants';
import { getUserHubPermission } from '@/lib/permissions';
import { notFound, redirect } from 'next/navigation';
import { headers } from 'next/headers';

interface MembersLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    hubId: string;
  }>;
}

export default async function MembersLayout({ children, params }: MembersLayoutProps) {
  const { hubId } = await params;
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/members`);
  }

  // Check if user has at least moderator permissions
  const permissionLevel = await getUserHubPermission(session.user.id, hubId);

  if (permissionLevel < PermissionLevel.MODERATOR) {
    // User doesn't have sufficient permissions
    notFound();
  }

  return <>{children}</>;
}
