'use client';;
import { useState, useEffect, useCallback } from 'react';
import { useR<PERSON>er, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useTRPC } from '@/utils/trpc';
import type { AppealStatus } from '@/lib/generated/prisma/client';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { formatDistanceToNow } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Check,
  Clock,
  Filter,
  MessageCircle,
  MessageSquare,
  RotateCcw,
  Search,
  Shield,
  User,
  X,
  Eye,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";

interface AppealUser {
  id: string;
  name: string | null;
  image: string | null;
}

interface AppealInfraction {
  id: string;
  hubId: string;
  type: string;
  status: string;
  reason: string;
  expiresAt: string | null;
  createdAt: string;
  updatedAt: string;
  hub: {
    id: string;
    name: string;
    iconUrl: string;
  };
  user: AppealUser | null;
  moderator: AppealUser | null;
  serverId?: string | null;
  serverName?: string | null;
}

interface Appeal {
  id: string;
  infractionId: string;
  userId: string;
  reason: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  createdAt: string;
  updatedAt: string;
  user: AppealUser;
  infraction: AppealInfraction;
}

interface AppealsClientProps {
  hubId: string;
}

export function AppealsClient({ hubId }: AppealsClientProps) {
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [status, setStatus] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(searchParams.get('userId'));
  const [infractionId, setInfractionId] = useState<string | null>(searchParams.get('infractionId'));
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 15;

  const {
    data,
    isLoading,
    error: queryError,
    refetch,
  } = useQuery(trpc.appeal.list.queryOptions({
    hubId,
    status: (status as AppealStatus) || undefined,
    userId: userId ?? undefined,
    infractionId: infractionId ?? undefined,
    page,
    limit: itemsPerPage,
  }));

  useEffect(() => {
    if (data) {
      setAppeals(data.appeals as unknown as Appeal[]);
      setTotalPages(Math.ceil((data.total ?? 0) / itemsPerPage) || 1);
    }
  }, [data, itemsPerPage]);

  useEffect(() => {
    if (queryError) {
      const message = (queryError as { message?: string })?.message || 'Failed to fetch appeals';
      setError(message);
      toast({ title: 'Error', description: message, variant: 'destructive' });
    } else {
      setError(null);
    }
  }, [queryError, toast]);

  useEffect(() => {
    // refetch on param changes
    refetch();
  }, [hubId, status, userId, infractionId, page, refetch]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Reset pagination when searching
    setPage(1);

    // Update URL with search parameters
    const params = new URLSearchParams();
    if (searchQuery.startsWith('user:')) {
      const userId = searchQuery.substring(5).trim();
      setUserId(userId);
      params.set('userId', userId);
      setInfractionId(null);
    } else if (searchQuery.startsWith('infraction:')) {
      const infractionId = searchQuery.substring(11).trim();
      setInfractionId(infractionId);
      params.set('infractionId', infractionId);
      setUserId(null);
    } else {
      // If no prefix, assume it's a user ID
      if (searchQuery) {
        setUserId(searchQuery);
        params.set('userId', searchQuery);
        setInfractionId(null);
      } else {
        setUserId(null);
        setInfractionId(null);
      }
    }

    // Update the URL without refreshing the page
    const url = new URL(window.location.href);
    url.search = params.toString();
    window.history.pushState({}, '', url.toString());

    refetch();
  };

  const resetFilters = () => {
    setStatus(null);
    setUserId(null);
    setInfractionId(null);
    setSearchQuery('');
    setPage(1);

    // Clear URL parameters
    router.push(`/dashboard/hubs/${hubId}/appeals`);
  };

  const handleAppealUpdate = useCallback((appealId: string, newStatus: 'ACCEPTED' | 'REJECTED') => {
    setAppeals((prevAppeals) =>
      prevAppeals.map((appeal) =>
        appeal.id === appealId ? { ...appeal, status: newStatus } : appeal,
      ),
    );
  }, []);

  // Count appeals by status (use real-time state)
  const pendingAppeals = appeals.filter((appeal) => appeal.status === 'PENDING').length;
  const acceptedAppeals = appeals.filter((appeal) => appeal.status === 'ACCEPTED').length;
  const rejectedAppeals = appeals.filter((appeal) => appeal.status === 'REJECTED').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-white">Hub Appeals</h1>
          <p className="text-g ray-400 text-sm">Review and manage appeals for this hub</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <MessageCircle className="h-4 w-4 text-blue-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Total Appeals</p>
                <p className="text-xl font-bold text-white">{appeals.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Clock className="h-4 w-4 text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Pending</p>
                <p className="text-xl font-bold text-white">{pendingAppeals}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <Check className="h-4 w-4 text-green-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Accepted</p>
                <p className="text-xl font-bold text-white">{acceptedAppeals}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <X className="h-4 w-4 text-red-400" />
              </div>
              <div>
                <p className="text-sm text-gray-400">Rejected</p>
                <p className="text-xl font-bold text-white">{rejectedAppeals}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="premium-card">
        <CardHeader className="pb-3 border-b border-gray-800/50">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2 text-indigo-400" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm text-gray-400 mb-1 block font-medium">Status</label>
              <Select
                value={status || 'all'}
                onValueChange={(value) => setStatus(value === 'all' ? null : value)}
              >
                <SelectTrigger className="bg-gray-900/50 border-gray-800 hover:bg-gray-900 transition-colors cursor-pointer">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-800">
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="ACCEPTED">Accepted</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="md:col-span-2">
              <label className="text-sm text-gray-400 mb-1 block font-medium">Search</label>
              <form onSubmit={handleSearch} className="flex flex-wrap gap-2">
                <div className="flex-1 min-w-[200px]">
                  <Input
                    placeholder="user:123456789 or infraction:abc123"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gray-900/50 border-gray-800 w-full hover:bg-gray-900 transition-colors"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    type="submit"
                    variant="secondary"
                    size="icon"
                    className="bg-indigo-600 hover:bg-indigo-700 text-white cursor-pointer"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={resetFilters}
                    className="border-gray-700 hover:bg-gray-800 hover:text-white cursor-pointer"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Appeals List */}
      <Card className="premium-card">
        <CardHeader className="pb-3 border-b border-gray-800/50">
          <CardTitle className="text-lg flex items-center">
            <MessageCircle className="h-4 w-4 mr-2 text-blue-400" />
            Appeals List
          </CardTitle>
          <CardDescription>
            {userId
              ? `Showing appeals from user ID: ${userId}`
              : infractionId
                ? `Showing appeals for infraction ID: ${infractionId}`
                : 'Showing all appeals for this hub'}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <AppealSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8 bg-red-500/10 rounded-lg border border-red-500/20 p-6">
              <AlertTriangle className="h-12 w-12 mx-auto text-red-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">Error Loading Appeals</h3>
              <p className="text-gray-400 mb-4">{error}</p>
              <Button onClick={() => refetch()} className="bg-red-600 hover:bg-red-700 text-white">
                Try Again
              </Button>
            </div>
          ) : appeals.length === 0 ? (
            <div className="text-center py-8 bg-gray-900/30 rounded-lg border border-gray-800/50 p-6">
              <MessageCircle className="h-12 w-12 mx-auto text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Appeals Found</h3>
              <p className="text-gray-400 mb-4">
                {userId
                  ? 'This user has not submitted any appeals for this hub.'
                  : infractionId
                    ? 'There are no appeals for this infraction.'
                    : 'There are no appeals for this hub yet.'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {appeals.map((appeal) => (
                <AppealCard
                  key={appeal.id}
                  appeal={appeal}
                  hubId={hubId}
                  onUpdate={handleAppealUpdate}
                />
              ))}
            </div>
          )}
        </CardContent>
        {!isLoading && appeals.length > 0 && totalPages > 1 && (
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t border-gray-800/50 pt-4 px-6 pb-6">
            <div className="text-sm text-gray-400 order-2 sm:order-1 flex items-center gap-2">
              <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
                <span className="text-xs">{page}</span>
              </div>
              <span>of {totalPages} pages</span>
            </div>
            <div className="flex gap-2 w-full sm:w-auto order-1 sm:order-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
                className="flex-1 sm:flex-initial border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className="flex-1 sm:flex-initial border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}

interface AppealCardProps {
  appeal: Appeal;
  hubId: string;
  onUpdate: (appealId: string, newStatus: 'ACCEPTED' | 'REJECTED') => void;
}

function AppealCard({ appeal, hubId, onUpdate }: AppealCardProps) {
  const trpc = useTRPC();
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);
  const [localStatus, setLocalStatus] = useState(appeal.status);
  const updateStatus = useMutation(trpc.appeal.updateStatus.mutationOptions());

  const handleStatusChange = async (newStatus: 'ACCEPTED' | 'REJECTED') => {
    setIsUpdating(true);
    const previousStatus = localStatus;
    setLocalStatus(newStatus);
    updateStatus.mutate(
      { appealId: appeal.id, status: newStatus },
      {
        onSuccess: () => {
          toast({
            title: `Appeal ${newStatus === 'ACCEPTED' ? 'Accepted' : 'Rejected'}`,
            description:
              newStatus === 'ACCEPTED'
                ? 'The infraction has been appealed.'
                : 'The appeal has been rejected.',
          });
          onUpdate(appeal.id, newStatus);
        },
        onError: (error) => {
          setLocalStatus(previousStatus);
          toast({
            title: 'Error',
            description: error.message || `Failed to ${newStatus.toLowerCase()} appeal`,
            variant: 'destructive',
          });
        },
        onSettled: () => setIsUpdating(false),
      },
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-yellow-500/20 text-yellow-400 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Pending
          </span>
        );
      case 'ACCEPTED':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400 flex items-center gap-1">
            <Check className="h-3 w-3" />
            Accepted
          </span>
        );
      case 'REJECTED':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-500/20 text-red-400 flex items-center gap-1">
            <X className="h-3 w-3" />
            Rejected
          </span>
        );
      default:
        return null;
    }
  };

  const getInfractionTypeBadge = (type: string) => {
    switch (type) {
      case 'BLACKLIST':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-500/20 text-red-400 flex items-center gap-1">
            <Shield className="h-3 w-3" />
            Blacklist
          </span>
        );
      case 'WARNING':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-orange-500/20 text-orange-400 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Warning
          </span>
        );
      default:
        return null;
    }
  };

  const submittedAt = formatDistanceToNow(new Date(appeal.createdAt), {
    addSuffix: true,
  });

  return (
    <div className="rounded-lg overflow-hidden premium-card">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0 px-4 py-3 border-b border-gray-800 bg-gray-900/50">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-gray-700/50 overflow-hidden">
            <Image
              src={appeal.user.image || 'https://api.dicebear.com/7.x/shapes/svg?seed=user'}
              alt={appeal.user.name || 'Unknown User'}
              width={40}
              height={40}
              className="object-cover"
              style={{ width: '100%', height: '100%' }}
              unoptimized
            />
          </div>
          <div>
            <div className="font-medium text-white">{appeal.user.name || 'Unknown User'}</div>
            <div className="text-xs text-gray-400">User ID: {appeal.userId}</div>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {getInfractionTypeBadge(appeal.infraction.type)}
          {getStatusBadge(localStatus)}
          <div className="text-xs text-gray-400 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {submittedAt}
          </div>
        </div>
      </div>

      <div className="p-4">
        {/* Appeal Reason */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-300 mb-2">Appeal Reason:</h4>
          <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
            <p className="text-sm text-gray-300">{appeal.reason}</p>
          </div>
        </div>

        {/* Original Infraction Details */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
            <Shield className="h-4 w-4 text-red-400" />
            Original Infraction Details
          </h4>

          <div className="space-y-4">
            {/* Infraction Header */}
            <div className="p-4 rounded-lg bg-gray-900/70 border border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getInfractionTypeBadge(appeal.infraction.type)}
                  <Badge
                    variant="outline"
                    className={
                      appeal.infraction.status === 'ACTIVE'
                        ? 'border-green-500/50 text-green-400 bg-green-500/10'
                        : appeal.infraction.status === 'REVOKED'
                          ? 'border-gray-500/50 text-gray-400 bg-gray-500/10'
                          : 'border-blue-500/50 text-blue-400 bg-blue-500/10'
                    }
                  >
                    {appeal.infraction.status}
                  </Badge>
                </div>
                <div className="text-xs text-gray-400">ID: {appeal.infraction.id}</div>
              </div>

              {/* Infraction Metadata */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4 p-3 bg-gray-800/50 rounded border border-gray-700/50">
                <div className="text-xs">
                  <span className="text-gray-400">Issued:</span>
                  <div className="text-gray-300 mt-1">
                    {new Date(appeal.infraction.createdAt).toLocaleString()}
                  </div>
                </div>
                {appeal.infraction.expiresAt && (
                  <div className="text-xs">
                    <span className="text-gray-400">Expires:</span>
                    <div className="text-gray-300 mt-1">
                      {new Date(appeal.infraction.expiresAt).toLocaleString()}
                    </div>
                  </div>
                )}
                <div className="text-xs">
                  <span className="text-gray-400">Target:</span>
                  <div className="text-gray-300 mt-1">
                    {appeal.infraction.user ? (
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3" />
                        {appeal.infraction.user.name || 'Unknown User'}
                      </div>
                    ) : appeal.infraction.serverName ? (
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-3 w-3" />
                        {appeal.infraction.serverName}
                      </div>
                    ) : (
                      'Unknown Target'
                    )}
                  </div>
                </div>
                {appeal.infraction.moderator && (
                  <div className="text-xs">
                    <span className="text-gray-400">Issued by:</span>
                    <div className="flex items-center gap-2 mt-1">
                      {appeal.infraction.moderator.image && (
                        <Image
                          src={appeal.infraction.moderator.image}
                          alt={appeal.infraction.moderator.name || 'Moderator'}
                          width={16}
                          height={16}
                          className="rounded-full"
                          unoptimized
                        />
                      )}
                      <span className="text-gray-300">
                        {appeal.infraction.moderator.name || 'Unknown Moderator'}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Infraction Reason */}
              <div className="bg-gray-800/50 p-3 rounded border border-gray-700/50">
                <div className="text-xs text-gray-400 mb-2">Infraction Reason:</div>
                <div className="text-sm text-gray-200 leading-relaxed">
                  {appeal.infraction.reason}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
          <Button
            variant="outline"
            size="sm"
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-blue-400 transition-all w-full sm:w-auto"
            asChild
          >
            <Link href={`/dashboard/hubs/${hubId}/infractions/${appeal.infractionId}/view`}>
              <Eye className="h-3 w-3 mr-1" />
              View Infraction
            </Link>
          </Button>

          {localStatus === 'PENDING' && (
            <>
              <Button
                variant="outline"
                size="sm"
                className="border-red-500/30 text-red-500 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 w-full sm:w-auto"
                onClick={() => handleStatusChange('REJECTED')}
                disabled={isUpdating}
              >
                <X className="h-4 w-4 mr-1" />
                Reject
              </Button>
              <Button
                variant="default"
                size="sm"
                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-600/80 hover:to-green-700/80 border-none w-full sm:w-auto"
                onClick={() => handleStatusChange('ACCEPTED')}
                disabled={isUpdating}
              >
                <Check className="h-4 w-4 mr-1" />
                Accept
              </Button>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

function AppealSkeleton() {
  return (
    <div className="rounded-lg overflow-hidden premium-card">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0 px-4 py-3 border-b border-gray-800 bg-gray-900/50">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Skeleton className="h-6 w-20 rounded-full" />
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </div>

      <div className="p-4">
        <div className="mb-4">
          <Skeleton className="h-4 w-24 mb-2" />
          <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4 mt-2" />
          </div>
        </div>

        <div className="mb-4">
          <Skeleton className="h-4 w-32 mb-2" />
          <div className="p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3 mt-2" />
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
          <Skeleton className="h-9 w-full sm:w-32" />
          <Skeleton className="h-9 w-full sm:w-20" />
          <Skeleton className="h-9 w-full sm:w-20" />
        </div>
      </div>
    </div>
  );
}
