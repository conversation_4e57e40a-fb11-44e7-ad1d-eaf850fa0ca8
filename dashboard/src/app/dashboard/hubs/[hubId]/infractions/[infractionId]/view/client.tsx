'use client';;
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  ArrowLeft,
  Shield,
  User,
  Server,
  AlertTriangle,
  Calendar,
  Eye,
  Ban,
  Edit,
  XCircle,
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { formatDistanceToNow } from 'date-fns';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";
import { useMutation } from "@tanstack/react-query";

interface Infraction {
  id: string;
  hubId: string;
  moderatorId: string;
  reason: string;
  expiresAt: string | Date | null;
  userId: string | null;
  serverId: string | null;
  serverName: string | null;
  createdAt: string | Date;
  updatedAt: string | Date;
  type: 'BAN' | 'BLACKLIST' | 'WARNING' | 'MUTE';
  status: 'ACTIVE' | 'REVOKED' | 'APPEALED';
  notified: boolean;
  appealedAt?: string | Date | null;
  hub: {
    id: string;
    name: string;
    iconUrl: string | null;
  };
  moderator: {
    id: string;
    name: string;
    image: string | null;
  };
  user?: {
    id: string;
    name: string;
    image: string | null;
  } | null;
  appeals: Array<{ id: string }>;
}

interface ViewInfractionClientProps {
  hubId: string;
  infractionId: string;
  canModifyDuration: boolean;
}

export function ViewInfractionClient({
  hubId,
  infractionId,
  canModifyDuration,
}: ViewInfractionClientProps) {
  const trpc = useTRPC();
  const { toast } = useToast();

  const [infraction, setInfraction] = useState<Infraction | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditDurationOpen, setIsEditDurationOpen] = useState(false);
  const [newExpiresAt, setNewExpiresAt] = useState('');
  const [isPermanent, setIsPermanent] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    data,
    isLoading,
    error: queryError,
  } = useQuery(trpc.moderation.getInfractionById.queryOptions({ infractionId }));

  useEffect(() => {
    setLoading(isLoading);
    if (queryError) {
      const msg = queryError instanceof Error ? queryError.message : 'Failed to load infraction';
      setError(msg);
    } else {
      setError(null);
    }
    if (data?.infraction) {
      setInfraction(data.infraction as Infraction);
      if (data.infraction.expiresAt) {
        setNewExpiresAt(new Date(data.infraction.expiresAt).toISOString().slice(0, 16));
        setIsPermanent(false);
      } else {
        setIsPermanent(true);
      }
    }
  }, [data, isLoading, queryError]);

  const updateInfraction = useMutation(trpc.moderation.updateInfraction.mutationOptions());
  const handleUpdateDuration = async () => {
    if (!infraction) return;

    try {
      setIsUpdating(true);
      const result = await updateInfraction.mutateAsync({
        infractionId,
        expiresAt: isPermanent ? null : newExpiresAt,
      });
      setInfraction(result.infraction as Infraction);
      setIsEditDurationOpen(false);

      toast({
        title: 'Duration Updated',
        description: isPermanent
          ? 'Infraction is now permanent'
          : 'Infraction duration has been updated',
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating duration:', error);
      toast({
        title: 'Error',
        description: 'Failed to update infraction duration',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getInfractionTypeInfo = (type: string) => {
    switch (type) {
      case 'BLACKLIST':
        return {
          label: 'Blacklist',
          icon: Ban,
          color: 'bg-red-500/20 text-red-400 border-red-500/50',
        };
      case 'WARNING':
        return {
          label: 'Warning',
          icon: AlertTriangle,
          color: 'bg-yellow-500/20 text-yellow-400 border-yellow-500/50',
        };
      case 'BAN':
        return {
          label: 'Ban',
          icon: Shield,
          color: 'bg-red-500/20 text-red-400 border-red-500/50',
        };
      case 'MUTE':
        return {
          label: 'Mute',
          icon: XCircle,
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/50',
        };
      default:
        return {
          label: type,
          icon: Shield,
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/50',
        };
    }
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return {
          label: 'Active',
          color: 'bg-emerald-500/20 text-emerald-400 border-emerald-500/50',
        };
      case 'REVOKED':
        return {
          label: 'Revoked',
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/50',
        };
      case 'APPEALED':
        return {
          label: 'Appealed',
          color: 'bg-blue-500/20 text-blue-400 border-blue-500/50',
        };
      default:
        return {
          label: status,
          color: 'bg-gray-500/20 text-gray-400 border-gray-500/50',
        };
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center gap-4">
          <div className="w-8 h-8 bg-gray-800/50 rounded animate-pulse" />
          <div className="space-y-2">
            <div className="w-48 h-6 bg-gray-800/50 rounded animate-pulse" />
            <div className="w-32 h-4 bg-gray-800/50 rounded animate-pulse" />
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="space-y-4">
          <div className="premium-card p-6 space-y-4">
            <div className="w-40 h-6 bg-gray-800/50 rounded animate-pulse" />
            <div className="space-y-2">
              <div className="w-full h-4 bg-gray-800/50 rounded animate-pulse" />
              <div className="w-3/4 h-4 bg-gray-800/50 rounded animate-pulse" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            asChild
            className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50"
          >
            <Link href={`/dashboard/hubs/${hubId}/infractions`}>
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight text-white">View Infraction</h1>
            <p className="text-gray-400 text-sm">Infraction details</p>
          </div>
        </div>

        <Alert className="border-red-500/50 bg-red-950/20">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!infraction) {
    return null;
  }

  const typeInfo = getInfractionTypeInfo(infraction.type);
  const statusInfo = getStatusInfo(infraction.status);
  const TypeIcon = typeInfo.icon;

  const isUserInfraction = infraction.userId !== null;
  const targetName = isUserInfraction
    ? infraction.user?.name || 'Unknown User'
    : infraction.serverName || 'Unknown Server';
  const targetId = isUserInfraction ? infraction.userId : infraction.serverId;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          asChild
          className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50"
        >
          <Link href={`/dashboard/hubs/${hubId}/infractions`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-white">View Infraction</h1>
          <p className="text-gray-400 text-sm">Infraction details and management</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Infraction Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Target Information */}
          <Card className="premium-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className={`p-2 rounded-[var(--radius-button)] ${typeInfo.color}`}>
                  <TypeIcon className="h-5 w-5" />
                </div>
                {typeInfo.label} Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Target Info */}
              <div className="flex items-center gap-4 p-4 bg-gray-900/50 rounded-[var(--radius)] border border-gray-700/50">
                {isUserInfraction ? (
                  <>
                    <Image
                      src={infraction.user?.image || '/pfp1.png'}
                      alt={targetName}
                      width={48}
                      height={48}
                      className="rounded-full border-2 border-gray-700"
                    />
                    <div>
                      <div className="font-medium text-white">{targetName}</div>
                      <div className="text-sm text-gray-400">User ID: {targetId}</div>
                    </div>
                    <Badge
                      variant="outline"
                      className="ml-auto bg-blue-500/20 text-blue-400 border-blue-500/50"
                    >
                      <User className="h-3 w-3 mr-1" />
                      User
                    </Badge>
                  </>
                ) : (
                  <>
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center border-2 border-gray-700">
                      <Server className="h-6 w-6 text-green-400" />
                    </div>
                    <div>
                      <div className="font-medium text-white">{targetName}</div>
                      <div className="text-sm text-gray-400">Server ID: {targetId}</div>
                    </div>
                    <Badge
                      variant="outline"
                      className="ml-auto bg-green-500/20 text-green-400 border-green-500/50"
                    >
                      <Server className="h-3 w-3 mr-1" />
                      Server
                    </Badge>
                  </>
                )}
              </div>

              {/* Status and Type */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-400">Status</Label>
                  <Badge variant="outline" className={`w-fit ${statusInfo.color}`}>
                    {statusInfo.label}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-400">Type</Label>
                  <Badge variant="outline" className={`w-fit ${typeInfo.color}`}>
                    <TypeIcon className="h-3 w-3 mr-1" />
                    {typeInfo.label}
                  </Badge>
                </div>
              </div>

              {/* Reason */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-400">Reason</Label>
                <div className="p-3 bg-gray-900/50 rounded-[var(--radius-button)] border border-gray-700/50">
                  <p className="text-gray-300 whitespace-pre-wrap">{infraction.reason}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Duration Information */}
          <Card className="premium-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-purple-500/20 rounded-[var(--radius-button)]">
                    <Calendar className="h-5 w-5 text-purple-400" />
                  </div>
                  Duration
                </div>
                {canModifyDuration && infraction.status === 'ACTIVE' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditDurationOpen(true)}
                    className="border-purple-500/50 bg-purple-500/10 text-purple-400 hover:bg-purple-500/20"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit Duration
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-400">Created</Label>
                  <div className="text-gray-300">
                    {new Date(infraction.createdAt).toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(infraction.createdAt), { addSuffix: true })}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-400">Expires</Label>
                  {infraction.expiresAt ? (
                    <>
                      <div className="text-gray-300">
                        {new Date(infraction.expiresAt).toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(infraction.expiresAt) > new Date()
                          ? `Expires ${formatDistanceToNow(new Date(infraction.expiresAt), { addSuffix: true })}`
                          : 'Expired'}
                      </div>
                    </>
                  ) : (
                    <div className="text-yellow-400 font-medium">Permanent</div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Moderator Information */}
          <Card className="premium-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-indigo-500/20 rounded-[var(--radius-button)]">
                  <Shield className="h-5 w-5 text-indigo-400" />
                </div>
                Issued By
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Image
                  src={infraction.moderator.image || '/pfp1.png'}
                  alt={infraction.moderator.name}
                  width={40}
                  height={40}
                  className="rounded-full border-2 border-gray-700"
                />
                <div>
                  <div className="font-medium text-white">{infraction.moderator.name}</div>
                  <div className="text-sm text-gray-400">Moderator</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Appeals Information */}
          {infraction.appeals.length > 0 && (
            <Card className="premium-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-blue-500/20 rounded-[var(--radius-button)]">
                    <Eye className="h-5 w-5 text-blue-400" />
                  </div>
                  Appeals
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-blue-400 font-medium">
                  {infraction.appeals.length} appeal{infraction.appeals.length !== 1 ? 's' : ''}{' '}
                  submitted
                </div>
                {infraction.appealedAt && (
                  <div className="text-sm text-gray-400 mt-1">
                    Last appeal:{' '}
                    {formatDistanceToNow(new Date(infraction.appealedAt), { addSuffix: true })}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Hub Information */}
          <Card className="premium-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-emerald-500/20 rounded-[var(--radius-button)]">
                  <Server className="h-5 w-5 text-emerald-400" />
                </div>
                Hub
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-3">
                <Image
                  src={infraction.hub.iconUrl || '/default-server.svg'}
                  alt={infraction.hub.name}
                  width={40}
                  height={40}
                  className="rounded-full border-2 border-gray-700"
                />
                <div>
                  <div className="font-medium text-white">{infraction.hub.name}</div>
                  <div className="text-sm text-gray-400">Hub</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="premium-card">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50"
                asChild
              >
                <Link href={`/dashboard/hubs/${hubId}/infractions`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Infractions
                </Link>
              </Button>

              {isUserInfraction && (
                <Button
                  variant="outline"
                  className="w-full border-blue-700/50 bg-blue-950/20 text-blue-400 hover:bg-blue-900/30"
                  asChild
                >
                  <Link href={`/dashboard/hubs/${hubId}/infractions?userId=${infraction.userId}`}>
                    <User className="h-4 w-4 mr-2" />
                    View User Infractions
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Duration Dialog */}
      <Dialog open={isEditDurationOpen} onOpenChange={setIsEditDurationOpen}>
        <DialogContent className="bg-gray-900 border-gray-800">
          <DialogHeader>
            <DialogTitle>Edit Infraction Duration</DialogTitle>
            <DialogDescription>
              Modify when this infraction expires. This action will be logged.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="permanent"
                checked={isPermanent}
                onChange={(e) => setIsPermanent(e.target.checked)}
                className="rounded border-gray-600 bg-gray-800 text-purple-500 focus:ring-purple-500"
              />
              <Label htmlFor="permanent" className="text-sm">
                Make permanent (no expiration)
              </Label>
            </div>

            {!isPermanent && (
              <div className="space-y-2">
                <Label htmlFor="expires-at" className="text-sm font-medium">
                  Expires At
                </Label>
                <Input
                  id="expires-at"
                  type="datetime-local"
                  value={newExpiresAt}
                  onChange={(e) => setNewExpiresAt(e.target.value)}
                  min={new Date().toISOString().slice(0, 16)}
                  className="bg-gray-800 border-gray-700"
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDurationOpen(false)}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateDuration}
              disabled={isUpdating || (!isPermanent && !newExpiresAt)}
              className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600"
            >
              {isUpdating ? 'Updating...' : 'Update Duration'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
