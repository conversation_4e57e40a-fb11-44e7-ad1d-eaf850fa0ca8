'use client';;
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { InfractionStatus, InfractionType } from '@/lib/generated/prisma/client';
import { formatDistanceToNow } from 'date-fns';
import {
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  Ban,
  Clock,
  Eye,
  Filter,
  Home,
  HomeIcon,
  MessageSquare,
  PlusCircle,
  RotateCcw,
  Search,
  Server,
  Shield,
  User,
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useTRPC } from '@/utils/trpc';

import { useQuery } from "@tanstack/react-query";

interface InfractionUser {
  id: string;
  name: string | null;
  image: string | null;
}

interface InfractionHub {
  id: string;
  name: string;
  iconUrl: string;
}

interface Infraction {
  id: string;
  hubId: string;
  type: InfractionType;
  status: InfractionStatus;
  moderatorId: string;
  reason: string;
  expiresAt: string | null;
  appealedAt: string | null;
  appealedBy: string | null;
  notified: boolean;
  userId: string | null;
  serverId: string | null;
  serverName: string | null;
  createdAt: string;
  updatedAt: string;
  hub: InfractionHub;
  moderator: InfractionUser | null;
  user: InfractionUser | null;
}

interface InfractionsClientProps {
  hubId: string;
}

export function InfractionsClient({ hubId }: InfractionsClientProps) {
  const trpc = useTRPC();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();

  const [infractions, setInfractions] = useState<Infraction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [type, setType] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [targetType, setTargetType] = useState<string | null>(null);
  const [userId, setUserId] = useState<string | null>(searchParams.get('userId'));
  const [serverId, setServerId] = useState<string | null>(searchParams.get('serverId'));
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 15;

  const typeFilter =
    type === 'BLACKLIST' || type === 'WARNING' ? (type as unknown as InfractionType) : undefined;
  const statusFilter =
    status === 'ACTIVE' || status === 'REVOKED' || status === 'APPEALED'
      ? (status as unknown as InfractionStatus)
      : undefined;
  const targetTypeFilter =
    targetType === 'user' || targetType === 'server' ? targetType : undefined;

  const {
    data,
    isLoading,
    error: queryError,
    refetch,
  } = useQuery(trpc.moderation.getInfractions.queryOptions({
    hubId,
    type: typeFilter,
    status: statusFilter,
    targetType: targetTypeFilter,
    userId: userId || undefined,
    serverId: serverId || undefined,
    page,
    limit: itemsPerPage,
  }));

  useEffect(() => {
    setLoading(isLoading);
    if (queryError) {
      const msg = queryError instanceof Error ? queryError.message : 'Failed to fetch infractions';
      setError(msg);
      toast({ title: 'Error', description: msg, variant: 'destructive' });
    } else {
      setError(null);
    }
    if (data) {
      setInfractions(data.infractions as unknown as Infraction[]);
      setTotalPages(data.totalPages || Math.ceil((data.total || 0) / itemsPerPage) || 1);
    }
  }, [data, isLoading, queryError, itemsPerPage, toast]);

  useEffect(() => {
    // trigger refetch when filters change via input bindings
    refetch();
  }, [hubId, type, status, targetType, userId, serverId, page, refetch]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Reset pagination when searching
    setPage(1);

    // Update URL with search parameters
    const params = new URLSearchParams();
    if (searchQuery.startsWith('user:')) {
      const userId = searchQuery.substring(5).trim();
      setUserId(userId);
      params.set('userId', userId);
      setServerId(null);
    } else if (searchQuery.startsWith('server:')) {
      const serverId = searchQuery.substring(7).trim();
      setServerId(serverId);
      params.set('serverId', serverId);
      setUserId(null);
    } else {
      // If no prefix, assume it's a user ID
      if (searchQuery) {
        setUserId(searchQuery);
        params.set('userId', searchQuery);
        setServerId(null);
      } else {
        setUserId(null);
        setServerId(null);
      }
    }

    // Update the URL without refreshing the page
    const url = new URL(window.location.href);
    url.search = params.toString();
    window.history.pushState({}, '', url.toString());

    refetch();
  };

  const resetFilters = () => {
    setType(null);
    setStatus(null);
    setTargetType(null);
    setUserId(null);
    setServerId(null);
    setSearchQuery('');
    setPage(1);

    // Clear URL parameters
    router.push(`/dashboard/hubs/${hubId}/infractions`);
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight text-white">Hub Infractions</h1>
          <p className="text-gray-400 text-sm">Manage and review infractions for this hub</p>
        </div>
        <Button
          asChild
          className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white border-none"
        >
          <Link href={`/dashboard/hubs/${hubId}/infractions/add`}>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Infraction
          </Link>
        </Button>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Total Infractions</p>
                <p className="text-2xl font-bold text-white mt-1">{infractions.length}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-purple-500/20 to-indigo-500/20 rounded-[var(--radius-button)] group-hover:scale-110 transition-transform">
                <Shield className="h-5 w-5 text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Active</p>
                <p className="text-2xl font-bold text-emerald-400 mt-1">
                  {infractions.filter((i) => i.status === 'ACTIVE').length}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-emerald-500/20 to-green-500/20 rounded-[var(--radius-button)] group-hover:scale-110 transition-transform">
                <AlertTriangle className="h-5 w-5 text-emerald-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Appeals</p>
                <p className="text-2xl font-bold text-blue-400 mt-1">
                  {infractions.filter((i) => i.appealedAt).length}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-[var(--radius-button)] group-hover:scale-110 transition-transform">
                <MessageSquare className="h-5 w-5 text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="premium-card group hover:scale-[1.02] transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-400">Revoked</p>
                <p className="text-2xl font-bold text-gray-400 mt-1">
                  {infractions.filter((i) => i.status === 'REVOKED').length}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-gray-500/20 to-slate-500/20 rounded-[var(--radius-button)] group-hover:scale-110 transition-transform">
                <RotateCcw className="h-5 w-5 text-gray-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Filters */}
      <Card className="premium-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <div className="p-2 bg-indigo-500/20 rounded-[var(--radius-button)]">
              <Filter className="h-4 w-4 text-indigo-400" />
            </div>
            Advanced Filters
          </CardTitle>
          <CardDescription>
            Filter infractions by type, status, target, or search for specific IDs
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <Label htmlFor="type-filter" className="text-sm font-medium text-gray-300">
                Infraction Type
              </Label>
              <Select
                value={type || 'all'}
                onValueChange={(value) => setType(value === 'all' ? null : value)}
              >
                <SelectTrigger
                  id="type-filter"
                  className="bg-gray-900/50 border-gray-700/50 hover:bg-gray-800/50 focus:border-purple-500/50 transition-all rounded-[var(--radius-button)]"
                >
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700/50 rounded-[var(--radius-button)]">
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="BLACKLIST">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full" />
                      Blacklist
                    </div>
                  </SelectItem>
                  <SelectItem value="WARNING">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full" />
                      Warning
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter" className="text-sm font-medium text-gray-300">
                Status
              </Label>
              <Select
                value={status || 'all'}
                onValueChange={(value) => setStatus(value === 'all' ? null : value)}
              >
                <SelectTrigger
                  id="status-filter"
                  className="bg-gray-900/50 border-gray-700/50 hover:bg-gray-800/50 focus:border-emerald-500/50 transition-all rounded-[var(--radius-button)]"
                >
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700/50 rounded-[var(--radius-button)]">
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="ACTIVE">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full" />
                      Active
                    </div>
                  </SelectItem>
                  <SelectItem value="REVOKED">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-gray-500 rounded-full" />
                      Revoked
                    </div>
                  </SelectItem>
                  <SelectItem value="APPEALED">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      Appealed
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="target-filter" className="text-sm font-medium text-gray-300">
                Target Type
              </Label>
              <Select
                value={targetType || 'all'}
                onValueChange={(value) => setTargetType(value === 'all' ? null : value)}
              >
                <SelectTrigger
                  id="target-filter"
                  className="bg-gray-900/50 border-gray-700/50 hover:bg-gray-800/50 focus:border-blue-500/50 transition-all rounded-[var(--radius-button)]"
                >
                  <SelectValue placeholder="All Targets" />
                </SelectTrigger>
                <SelectContent className="bg-gray-900 border-gray-700/50 rounded-[var(--radius-button)]">
                  <SelectItem value="all">All Targets</SelectItem>
                  <SelectItem value="user">
                    <div className="flex items-center gap-2">
                      <User className="h-3 w-3 text-blue-400" />
                      Users Only
                    </div>
                  </SelectItem>
                  <SelectItem value="server">
                    <div className="flex items-center gap-2">
                      <Server className="h-3 w-3 text-orange-400" />
                      Servers Only
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="search-input" className="text-sm font-medium text-gray-300">
                Search by ID
              </Label>
              <form onSubmit={handleSearch} className="flex gap-2">
                <div className="flex-1">
                  <Input
                    id="search-input"
                    placeholder="user:123456789 or server:123456789"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gray-900/50 border-gray-700/50 hover:bg-gray-800/50 focus:border-indigo-500/50 transition-all rounded-[var(--radius-button)]"
                  />
                </div>
                <Button
                  type="submit"
                  size="icon"
                  className="bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 border-none rounded-[var(--radius-button)]"
                >
                  <Search className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={resetFilters}
                  className="border-gray-700/50 hover:bg-gray-800/50 hover:text-white rounded-[var(--radius-button)]"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </form>
              <p className="text-xs text-gray-400 mt-1">
                Use prefixes like &quot;user:&quot; or &quot;server:&quot; to search for specific
                IDs
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Infractions List */}
      <Card className="premium-card">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-xl">
            <div className="p-2 bg-purple-500/20 rounded-[var(--radius-button)]">
              <Shield className="h-5 w-5 text-purple-400" />
            </div>
            Infractions List
          </CardTitle>
          <CardDescription className="text-gray-300 mt-2">
            {(() => {
              if (userId) return `Showing infractions for user ID: ${userId}`;
              if (serverId) return `Showing infractions for server ID: ${serverId}`;
              return 'Showing all infractions for this hub';
            })()}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          {loading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, i) => (
                <InfractionSkeleton key={i} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-12 bg-gradient-to-br from-red-950/30 to-red-900/20 rounded-[var(--radius)] border border-red-500/30 p-8">
              <div className="p-4 bg-red-500/20 rounded-[var(--radius-button)] w-fit mx-auto mb-6">
                <AlertTriangle className="h-8 w-8 text-red-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">Error Loading Infractions</h3>
              <p className="text-gray-300 mb-6 max-w-md mx-auto">{error}</p>
              <Button
                onClick={() => refetch()}
                className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-none rounded-[var(--radius-button)] px-6"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : infractions.length === 0 ? (
            <div className="text-center py-12 bg-gradient-to-br from-gray-900/50 to-gray-800/30 rounded-[var(--radius)] border border-gray-700/50 p-8">
              <div className="p-4 bg-gray-500/20 rounded-[var(--radius-button)] w-fit mx-auto mb-6">
                <Shield className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">No Infractions Found</h3>
              <p className="text-gray-300 mb-6 max-w-md mx-auto">
                {(() => {
                  if (userId) return 'This user has no infractions in this hub.';
                  if (serverId) return 'This server has no infractions in this hub.';
                  return 'There are no infractions in this hub yet.';
                })()}
              </p>
              <Button
                asChild
                className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white border-none rounded-[var(--radius-button)] px-6"
              >
                <Link href={`/dashboard/hubs/${hubId}/infractions/add`}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create First Infraction
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {infractions.map((infraction) => (
                <InfractionCard key={infraction.id} infraction={infraction} />
              ))}
            </div>
          )}
        </CardContent>
        {!loading && infractions.length > 0 && (
          <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t border-gray-800/50 pt-4">
            <div className="text-sm text-gray-400 order-2 sm:order-1 flex items-center gap-2">
              <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
                <span className="text-xs">{page}</span>
              </div>
              <span>of {totalPages} pages</span>
            </div>
            <div className="flex gap-2 w-full sm:w-auto order-1 sm:order-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1}
                className="flex-1 sm:flex-initial border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className="flex-1 sm:flex-initial border-gray-700 hover:bg-gray-800 hover:text-white"
              >
                Next
                <ArrowRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}

interface InfractionCardProps {
  infraction: Infraction;
}

function InfractionCard({ infraction }: InfractionCardProps) {
  const isUserInfraction = !!infraction.userId;
  const targetName = isUserInfraction
    ? infraction.user?.name || 'Unknown User'
    : infraction.serverName || 'Unknown Server';

  const expiresAt = infraction.expiresAt ? new Date(infraction.expiresAt) : null;

  const expiresIn = expiresAt
    ? new Date() > expiresAt
      ? 'Expired'
      : formatDistanceToNow(expiresAt, { addSuffix: true })
    : 'Never';

  const createdAt = new Date(infraction.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  const getInfractionStatusBadge = (status: InfractionStatus) => {
    switch (status) {
      case 'ACTIVE':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-500/20 text-green-400 flex items-center gap-1">
            <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
            Active
          </span>
        );
      case 'REVOKED':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-gray-500/20 text-gray-400 flex items-center gap-1">
            <div className="w-1.5 h-1.5 rounded-full bg-gray-400"></div>
            Revoked
          </span>
        );
      case 'APPEALED':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400 flex items-center gap-1">
            <div className="w-1.5 h-1.5 rounded-full bg-blue-400"></div>
            Appealed
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="rounded-lg overflow-hidden border border-gray-800 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0 px-4 py-3 border-b border-gray-800 bg-gray-900/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            {isUserInfraction ? (
              <Image
                src={infraction.user?.image || '/pfp1.png'}
                alt={targetName}
                width={40}
                height={40}
                className="rounded-full border-2 border-gray-800"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center border-2 border-gray-800">
                <HomeIcon className="h-5 w-5 text-gray-300" />
              </div>
            )}
            <div className="absolute -bottom-1 -right-1 bg-gray-950 rounded-full p-0.5 border border-gray-700 shadow-md">
              {isUserInfraction ? (
                <User className="h-3 w-3 text-blue-400" />
              ) : (
                <Home className="h-3 w-3 text-green-400" />
              )}
            </div>
          </div>
          <div>
            <div className="font-medium text-white">{targetName}</div>
            <div
              className="text-xs text-gray-400 truncate max-w-[150px] sm:max-w-[200px] md:max-w-[250px] lg:max-w-none"
              title={(isUserInfraction ? infraction.userId : infraction.serverId) || undefined}
            >
              {isUserInfraction ? infraction.userId : infraction.serverId}
            </div>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          {infraction.type === 'BLACKLIST' ? (
            <span className="px-2 py-1 rounded-full text-xs bg-red-500/20 text-red-400 flex items-center gap-1">
              <Ban className="h-3 w-3" />
              Blacklist
            </span>
          ) : (
            <span className="px-2 py-1 rounded-full text-xs bg-yellow-500/20 text-yellow-400 flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              Warning
            </span>
          )}
          {getInfractionStatusBadge(infraction.status)}
          {infraction.appealedAt && (
            <span className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-400 flex items-center gap-1">
              <MessageSquare className="h-3 w-3" />
              Appeal
            </span>
          )}
        </div>
      </div>
      <div className="p-4">
        <div className="bg-gray-900/30 rounded-md p-3 mb-3 border border-gray-800/50">
          <p className="text-sm text-gray-300">{infraction.reason}</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-xs mb-3">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
              <Clock className="h-3 w-3 text-gray-400" />
            </div>
            <div>
              <span className="text-gray-400">Created:</span> {createdAt}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
              <User className="h-3 w-3 text-gray-400" />
            </div>
            <div>
              <span className="text-gray-400">By:</span> {infraction.moderator?.name || 'Unknown'}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
              <Clock className="h-3 w-3 text-gray-400" />
            </div>
            <div>
              <span className="text-gray-400">Expires:</span> {expiresIn}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-gray-800/50 flex items-center justify-center">
              <Shield className="h-3 w-3 text-gray-400" />
            </div>
            <div>
              <span className="text-gray-400">Status:</span>{' '}
              {infraction.status.charAt(0) + infraction.status.slice(1).toLowerCase()}
            </div>
          </div>
        </div>
        <div className="border-t border-gray-800/50 pt-3 mt-3">
          <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            {/* View Details button - always visible */}
            <Button
              variant="outline"
              size="sm"
              className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-indigo-400 transition-all w-full sm:w-auto"
              asChild
            >
              <Link href={`/dashboard/hubs/${infraction.hubId}/infractions/${infraction.id}/view`}>
                <Eye className="h-3 w-3 mr-1" />
                View Details
                {infraction.appealedAt && (
                  <span className="ml-1 px-1.5 py-0.5 text-[10px] rounded-full bg-blue-500/20 text-blue-400">
                    Appeal
                  </span>
                )}
              </Link>
            </Button>

            {/* Action buttons - only for ACTIVE infractions */}
            {infraction.status === 'ACTIVE' && (
              <>
                {/* Only show Extend button for BLACKLIST type */}
                {infraction.type === 'BLACKLIST' && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-blue-400 transition-all w-full sm:w-auto"
                    asChild
                  >
                    <Link href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}>
                      <Clock className="h-3 w-3 mr-1" />
                      Extend
                    </Link>
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  className="border-red-700/30 bg-red-950/20 text-red-400 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 transition-all w-full sm:w-auto"
                  asChild
                >
                  <Link href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}>
                    <Shield className="h-3 w-3 mr-1" />
                    Remove
                  </Link>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function InfractionSkeleton() {
  return (
    <div className="rounded-lg overflow-hidden border border-gray-800 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0 px-4 py-3 border-b border-gray-800 bg-gray-900/50">
        <div className="flex items-center gap-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-3 w-24" />
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Skeleton className="h-6 w-20 rounded-full" />
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-6 w-16 rounded-full" />
        </div>
      </div>

      <div className="p-4">
        <div className="bg-gray-900/30 rounded-md p-3 mb-3 border border-gray-800/50">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4 mt-2" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-3">
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-6 w-6 rounded-full" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>

        <div className="border-t border-gray-800/50 pt-3 mt-3">
          <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <Skeleton className="h-9 w-full sm:w-28" />
            <Skeleton className="h-9 w-full sm:w-20" />
            <Skeleton className="h-9 w-full sm:w-20" />
          </div>
        </div>
      </div>
    </div>
  );
}
