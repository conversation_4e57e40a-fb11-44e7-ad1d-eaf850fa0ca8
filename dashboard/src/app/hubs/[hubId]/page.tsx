'use server';

import HubDetailsCard from '../components/hub-detail/HubDetailsCard';
import HubModeratorsCard from '../components/hub-detail/HubModeratorsCard';
import SimilarHubsCard from '../components/hub-detail/SimilarHubsCard';
import { auth } from '@/auth';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PermissionLevel } from '@/lib/constants';
import { getUserHubPermission } from '@/lib/permissions';
import { getHubData, getHubConnections } from '@/lib/hub-queries';
import { cn } from '@/lib/utils';
import { Info, MessageSquare, ScrollText, Settings, Users } from 'lucide-react';
import type { Metadata } from 'next';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import ClientReviewSection from '../components/hub-detail/ClientReviewSection';
import HubBanner from '../components/hub-detail/HubBanner';
import HubConnectedServers from '../components/hub-detail/HubConnectedServers';
import HubInfoCard from '../components/hub-detail/HubInfoCard';
import HubOverview from '../components/hub-detail/HubOverview';
import HubReviewAnalytics from '../components/hub-detail/HubReviewAnalytics';
import HubRules from '../components/hub-detail/HubRules';
import JoinButton from '../components/hub-detail/JoinButton';
import ReviewItem from '../components/hub-detail/ReviewItem';
import UpvoteButton from '../components/hub-detail/UpvoteButton';
import { headers } from 'next/headers';

// Metadata function remains the same
export async function generateMetadata(props: {
  params: Promise<{ hubId: string }>;
}): Promise<Metadata> {
  const { hubId } = await props.params;
  const hub = await getHubData(hubId);

  if (!hub) return { title: 'Hub Not Found' };

  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://interchat.tech';

  return {
    title: `${hub.name} | InterChat Discord Community Hub`,
    description:
      hub.description ||
      'Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.',
    keywords: [
      'discord community',
      'discord hub',
      'connect discord servers',
      'interchat hub',
      ...(hub.tags.map((t) => t.name) || []),
    ],
    openGraph: {
      title: `${hub.name} | InterChat Discord Community Hub`,
      description:
        hub.description ||
        'Join this active Discord community hub with InterChat. Connect your server and start chatting with other communities.',
      type: 'website',
      url: `${baseUrl}/hubs/${hubId}`,
      images: [
        {
          url: hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
          width: 1200,
          height: 630,
          alt: hub.name,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      images: [hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`],
      title: `${hub.name} | InterChat Discord Community Hub`,
      description: hub.description || 'Join this active Discord community hub with InterChat.',
      creator: '@737_dev',
      site: '@interchatapp',
    },
    alternates: {
      canonical: `${baseUrl}/hubs/${hubId}`,
    },
  };
}

export default async function HubDetailView(props: { params: Promise<{ hubId: string }> }) {
  const { hubId } = await props.params;

  // Check user authentication and permissions
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  let userPermissionLevel = PermissionLevel.NONE;

  if (session?.user) {
    userPermissionLevel = await getUserHubPermission(session.user.id, hubId);
  }

  const canManageHub = userPermissionLevel >= PermissionLevel.MODERATOR;

  const hub = await getHubData(hubId);
  const connections = (await getHubConnections(hubId)) ?? [];

  if (!hub) {
    notFound();
  }

  const formattedDate = new Date(hub.createdAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Group reviews by rating to show analytics
  const reviewStats = {
    total: hub.reviews?.length || 0,
    average:
      hub.reviews?.reduce((acc, review) => acc + review.rating, 0) / (hub.reviews?.length || 1),
    distribution: [5, 4, 3, 2, 1].map((rating) => ({
      rating,
      count: hub.reviews?.filter((r) => r.rating === rating).length || 0,
      percentage: Math.round(
        ((hub.reviews?.filter((r) => r.rating === rating).length || 0) /
          (hub.reviews?.length || 1)) *
          100,
      ),
    })),
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-b from-gray-950 to-gray-900 text-gray-200">
      {/* Main Content */}
      <div className="flex-grow">
        {/* Enhanced Banner Section with taller height and parallax effect */}
        <HubBanner bannerUrl={hub.bannerUrl} name={hub.name} />

        {/* Page Content */}
        <div className="container mx-auto px-4 max-w-7xl pb-16">
          {/* Enhanced Header Card */}
          <div className="relative -mt-32 md:-mt-40 mb-12 transform transition-all duration-300">
            <div className="rounded-2xl border border-gray-800/70 bg-gray-900/90 p-6 md:p-8 shadow-xl backdrop-blur-xl">
              <div className="flex flex-col md:flex-row md:justify-between md:items-start w-full">
                <HubInfoCard hub={hub} />

                {/* Action Buttons with enhanced styling */}
                <div className="flex flex-shrink-0 gap-3 mt-6 md:mt-0 md:ml-auto p-2 rounded-lg border border-gray-700/50 bg-gray-800/40">
                  {/* Manage Hub button - only show for users with management permissions */}
                  {canManageHub && (
                    <Link href={`/dashboard/hubs/${hubId}`}>
                      <Button
                        className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-600/80 hover:to-purple-600/80 text-white font-medium rounded-lg border-0 transition-all duration-200 hover:scale-105 shadow-lg cursor-pointer"
                        size="sm"
                        variant="outline"
                      >
                        <Settings className="h-4 w-4" />
                        Manage Hub
                      </Button>
                    </Link>
                  )}

                  {/* Enhanced join button with animation */}
                  <div className="relative group">
                    <JoinButton hubName={hub.name} hubId={hub.id} />
                  </div>

                  {/* Enhanced upvote button */}
                  <UpvoteButton hubId={hub.id} initialUpvotes={hub.upvotes} />
                </div>
              </div>
            </div>
          </div>

          {/* Main Grid (Tabs/Reviews + Sidebar) with improved layout */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column (Tabs and Reviews) */}
            <div className="lg:col-span-2 space-y-8">
              {/* Enhanced Tabs Section */}
              <Tabs defaultValue="overview" className="w-full">
                {/* Tab Triggers with improved styling */}
                <div className="relative mb-6">
                  <TabsList className="inline-flex h-auto items-center justify-start w-full rounded-lg bg-gray-800/40 p-1.5 border border-gray-700/40 gap-1 sm:gap-2 px-4">
                    {[
                      { value: 'overview', icon: Info, label: 'Overview' },
                      { value: 'rules', icon: ScrollText, label: 'Rules' },
                      {
                        value: 'servers',
                        icon: Users,
                        label: 'Connected Servers',
                      },
                    ].map((tab) => (
                      <TabsTrigger
                        key={tab.value}
                        value={tab.value}
                        className={cn(
                          'cursor-pointer inline-flex items-center justify-center whitespace-nowrap rounded-md px-2 sm:px-3 md:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-medium ring-offset-background transition-all flex-shrink-0',
                          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                          'disabled:pointer-events-none disabled:opacity-50',
                          'data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary data-[state=active]:to-primary-alt data-[state=active]:text-white data-[state=active]:shadow-md', // Active state
                          'data-[state=active]:animate-tab-glow', // Animation for active tab
                          'text-gray-300 hover:bg-gray-700/50 hover:text-white', // Inactive state
                        )}
                      >
                        <tab.icon className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                        <span className="truncate">{tab.label}</span>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {/* Tab Content Panes with improved styling */}
                <TabsContent
                  value="overview"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md', // Container with padding
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2', // Standard focus styles
                  )}
                >
                  <HubOverview description={hub.description} />

                  {/* Reviews Section - Now part of Overview */}
                  <div className="mt-8 pt-8 border-t border-gray-700/50">
                    <div className="flex items-center gap-2 mb-6">
                      <MessageSquare className="h-5 w-5 text-primary" />
                      <h3 className="text-lg font-semibold text-white">Reviews</h3>
                    </div>

                    <HubReviewAnalytics reviewStats={reviewStats} />

                    {/* Review Form */}
                    <ClientReviewSection hubId={hub.id} />

                    {/* Review list */}
                    <div className="space-y-4">
                      {hub.reviews?.length > 0 ? (
                        <>
                          {hub.reviews.map((review) => (
                            <ReviewItem key={review.id} review={review} hubId={hub.id} />
                          ))}
                        </>
                      ) : (
                        <div className="flex flex-col items-center justify-center p-10 text-gray-400">
                          <MessageSquare className="h-12 w-12 text-gray-500 mb-3 opacity-50" />
                          <p className="text-center text-gray-400">No reviews yet</p>
                          <p className="text-center text-gray-500 text-sm mt-1">
                            Be the first to review this hub!
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent
                  value="rules"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md',
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                  )}
                >
                  <HubRules rules={hub.rules} />
                </TabsContent>

                <TabsContent
                  value="servers"
                  className={cn(
                    'rounded-xl border border-gray-800/70 bg-gray-900/60 p-6 md:p-8 shadow-lg backdrop-blur-md',
                    'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
                  )}
                >
                  <HubConnectedServers connections={connections} />
                </TabsContent>
              </Tabs>
            </div>
            {/* Right Column (Sidebar) */}
            <div className="lg:col-span-1 space-y-4 sm:space-y-6">
              <HubDetailsCard formattedDate={formattedDate} hub={hub} connections={connections} />

              <HubModeratorsCard moderators={hub.moderators} />

              <SimilarHubsCard />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
