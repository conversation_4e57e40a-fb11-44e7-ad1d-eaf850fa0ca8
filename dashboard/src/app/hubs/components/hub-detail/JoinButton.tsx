'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from '@/hooks/use-toast';
import { Check, ChevronDown, Copy, Home } from 'lucide-react';
import { useSession } from '@/lib/auth-client';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface JoinButtonProps {
  hubName: string;
  hubId: string;
}

export default function JoinButton({ hubName, hubId }: JoinButtonProps) {
  const [copied, setCopied] = useState(false);
  const joinHubCommand = `/connect hub:${hubName}`;
  const router = useRouter();
  const { data: session } = useSession();

  const handleJoin = async () => {
    try {
      await navigator.clipboard.writeText(joinHubCommand);
      setCopied(true);
      toast({
        title: 'Command copied!',
        description: 'Paste this command in your Discord server to join the hub.',
        className: 'bg-card border-primary/20 text-primary-foreground',
        duration: 3000,
      });
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast({
        title: 'Failed to copy',
        description: 'Please copy the command manually.',
        variant: 'destructive',
      });
    }
  };

  const handleConnectServer = () => {
    if (!session) {
      router.push(`/login?callbackUrl=/dashboard?hubId=${hubId}`);
      return;
    }

    // Redirect to servers page with hubId parameter
    router.push(`/dashboard?hubId=${hubId}`);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="default"
          className="bg-primary hover:bg-primary/90 text-white px-3 py-1 h-8 sm:h-9 text-xs sm:text-sm font-medium rounded-lg transition-all transform hover:scale-105 cursor-pointer w-full whitespace-nowrap shadow-sm hover:shadow-md"
        >
          {copied ? (
            <>
              <Check className="mr-1 sm:mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Copied!</span>
            </>
          ) : (
            <>
              <span>Join</span>
              <ChevronDown className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48 sm:w-56 bg-gray-900 border-gray-800">
        <DropdownMenuItem onClick={handleJoin} className="cursor-pointer">
          <Copy className="mr-2 h-4 w-4" />
          <span>Copy Discord Command</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleConnectServer} className="cursor-pointer">
          <Home className="mr-2 h-4 w-4" />
          <span>Connect Server</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
