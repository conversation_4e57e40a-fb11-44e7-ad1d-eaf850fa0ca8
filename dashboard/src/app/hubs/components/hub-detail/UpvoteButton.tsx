'use client';

import { Button } from '@/components/ui/button';
import { SimplifiedHub } from '@/hooks/use-infinite-hubs';
import { cn } from '@/lib/utils';
import { Heart, Loader2 } from 'lucide-react';
import { useHubUpvote } from '../../hooks/use-hub-upvote';

interface UpvoteButtonProps {
  hubId: string;
  initialUpvotes: SimplifiedHub['upvotes'];
}

export default function UpvoteButton({ hubId, initialUpvotes }: UpvoteButtonProps) {
  const { liked, upvoteCount, handleUpvote, isLoading } = useHubUpvote(hubId, initialUpvotes);

  return (
    <Button
      variant="outline"
      className={cn(
        'border-gray-700 bg-transparent rounded-lg cursor-pointer hover:bg-rose-500/10 hover:text-rose-400 active:scale-95 px-3 sm:px-4 py-2 text-sm w-full md:w-auto shadow-sm hover:shadow-md',
        liked && 'border-primary/50',
      )}
      onClick={handleUpvote}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2 animate-spin" />
      ) : (
        <Heart
          className={cn(
            'h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2',
            liked ? 'fill-rose-500 text-rose-500' : 'text-rose-500',
          )}
        />
      )}
      <span className="hidden sm:inline">{liked ? 'Upvoted' : 'Upvote'}</span>
      <span className="text-gray-400 ml-1 sm:ml-2">({upvoteCount})</span>
    </Button>
  );
}
