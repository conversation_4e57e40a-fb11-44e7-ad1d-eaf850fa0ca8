'use client';

import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ArrowRight, ChevronDown, Plus, Sparkles } from 'lucide-react';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Enhanced Accordion Component with improved animations and interactions
interface AccordionItemProps {
  readonly title: string;
  readonly content: React.ReactNode;
  readonly isOpen: boolean;
  readonly onToggle: () => void;
  readonly index: number;
}

function AccordionItem({ title, content, isOpen, onToggle, index }: AccordionItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.08 }}
      className="group relative"
    >
      {/* Hover glow effect */}
      <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/20 via-indigo-500/20 to-blue-500/20 rounded-[calc(var(--radius)+4px)] opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm" />

      <div
        className={cn(
          'relative bg-gradient-to-br from-gray-900/95 to-gray-950/95 backdrop-blur-xl',
          'border border-gray-800/60 hover:border-gray-700/80',
          'transition-all duration-500 overflow-hidden',
          'hover:shadow-2xl hover:shadow-purple-500/10',
          isOpen
            ? 'rounded-[var(--radius)] rounded-b-none shadow-2xl shadow-purple-500/20'
            : 'rounded-[var(--radius)]',
          'group-hover:scale-[1.02] group-hover:z-10',
        )}
      >
        {/* Enhanced background decoration with moving gradients */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-purple-500/8 via-transparent to-blue-500/8 opacity-0 group-hover:opacity-100 transition-opacity duration-700"
          animate={isOpen ? { opacity: [0.05, 0.15, 0.05] } : {}}
          transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}
        />

        {/* Subtle animated mesh pattern */}
        <div className="absolute inset-0 opacity-[0.02] group-hover:opacity-[0.05] transition-opacity duration-500">
          <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.4"%3E%3Ccircle cx="7" cy="7" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")] animate-pulse' />
        </div>

        {/* Glass morphism overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.08] via-white/[0.02] to-transparent pointer-events-none" />

        {/* Enhanced glow effect for open state */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-gradient-to-br from-purple-500/15 via-indigo-500/10 to-blue-500/15 rounded-[var(--radius)] rounded-b-none"
          />
        )}

        <button
          onClick={onToggle}
          className={cn(
            'relative w-full px-6 py-7 lg:px-8 lg:py-8',
            'flex items-center justify-between',
            'text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-400/60 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900',
            'transition-all duration-300',
            'hover:bg-gradient-to-r hover:from-gray-800/30 hover:to-gray-700/30',
          )}
          aria-expanded={isOpen}
          aria-controls={`faq-content-${index}`}
          tabIndex={0}
        >
          <div className="flex items-center gap-6 flex-1">
            {/* Enhanced icon with improved animations */}
            <motion.div
              className={cn(
                'relative flex items-center justify-center w-12 h-12 rounded-[var(--radius-button)]',
                'bg-gradient-to-br from-purple-500/20 to-blue-500/20',
                'border border-purple-500/30 backdrop-blur-sm',
                'transition-all duration-300',
                isOpen &&
                  'bg-gradient-to-br from-purple-500/30 to-blue-500/30 border-purple-400/50 shadow-xl shadow-purple-500/30',
              )}
              whileHover={{ scale: 1.05, rotate: [0, -5, 5, 0] }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              {/* Background glow */}
              <div className="absolute inset-0 rounded-[var(--radius-button)] bg-gradient-to-br from-purple-400/20 to-blue-400/20 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <motion.div
                animate={{ rotate: isOpen ? 45 : 0 }}
                transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
                className="relative z-10"
              >
                <Plus
                  className={cn(
                    'w-5 h-5 transition-colors duration-300',
                    isOpen ? 'text-purple-200' : 'text-purple-400 group-hover:text-purple-300',
                  )}
                />
              </motion.div>
            </motion.div>

            <h3 className="text-xl lg:text-2xl font-semibold text-white group-hover:text-purple-100 transition-colors duration-300 leading-tight">
              {title}
            </h3>
          </div>

          {/* Enhanced chevron indicator with micro-interactions */}
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
            className="ml-4 p-2 rounded-full hover:bg-white/5 transition-colors duration-200"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronDown
              className={cn(
                'w-5 h-5 transition-colors duration-300',
                isOpen ? 'text-purple-300' : 'text-gray-400 group-hover:text-gray-300',
              )}
            />
          </motion.div>
        </button>
      </div>

      {/* Enhanced content with improved animations */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            id={`faq-content-${index}`}
            initial={{ height: 0, opacity: 0, y: -10 }}
            animate={{ height: 'auto', opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -10 }}
            transition={{
              duration: 0.5,
              ease: [0.25, 0.46, 0.45, 0.94],
              opacity: { duration: 0.3, delay: 0.1 },
              y: { duration: 0.4 },
            }}
            className="overflow-hidden relative z-10"
          >
            <div
              className={cn(
                'bg-gradient-to-br from-gray-800/90 to-gray-900/95 backdrop-blur-xl',
                'border-x border-b border-gray-800/60',
                'rounded-b-[var(--radius)]',
                'relative shadow-2xl shadow-black/30',
              )}
            >
              {/* Enhanced content background decoration */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5"
              />

              {/* Subtle border gradient */}
              <div className="absolute inset-0 rounded-b-[var(--radius)] border border-transparent bg-gradient-to-br from-purple-500/10 via-transparent to-blue-500/10 [mask:linear-gradient(#fff_0_0)_content-box,linear-gradient(#fff_0_0)] [mask-composite:xor] pointer-events-none" />

              {/* Content area with improved spacing and typography */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.25, duration: 0.4 }}
                className="relative p-7 lg:p-9"
              >
                <div className="text-gray-200 leading-relaxed space-y-4 text-base lg:text-lg [&_a]:text-purple-400 [&_a]:hover:text-purple-300 [&_a]:transition-colors [&_a]:duration-200 [&_a]:underline [&_a]:underline-offset-2 [&_a]:decoration-purple-400/50 [&_a]:hover:decoration-purple-300/70">
                  {content}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export function FaqSection() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  if (!mounted) {
    return <div className="h-screen bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950" />;
  }

  return (
    <section className="relative py-24 md:py-40 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Enhanced background with more sophisticated gradients */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary radial gradient */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-800/30 via-gray-900/20 to-transparent" />

        {/* Animated gradient orbs with improved timing */}
        <motion.div
          animate={{
            opacity: [0.2, 0.4, 0.2],
            scale: [1, 1.1, 1],
            x: [0, 20, 0],
            y: [0, -10, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            times: [0, 0.5, 1],
          }}
          className="absolute top-1/4 left-1/5 w-[500px] h-[500px] bg-purple-500/8 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            opacity: [0.3, 0.6, 0.3],
            scale: [1.1, 1, 1.1],
            x: [0, -30, 0],
            y: [0, 15, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 2,
            times: [0, 0.5, 1],
          }}
          className="absolute bottom-1/4 right-1/5 w-[400px] h-[400px] bg-blue-500/8 rounded-full blur-3xl"
        />

        {/* Additional accent gradients */}
        <motion.div
          animate={{
            opacity: [0.1, 0.3, 0.1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: 'linear',
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[800px] h-[200px] bg-gradient-to-r from-purple-500/5 to-blue-500/5 rounded-full blur-2xl"
        />

        {/* Enhanced grid pattern */}
        <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="40" cy="40" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]' />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Enhanced header section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20 lg:mb-24 relative"
        >
          {/* Enhanced badge with sparkle effect */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="inline-block mb-8"
          >
            <div className="relative group">
              {/* Badge glow */}
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-[calc(var(--radius-badge)+4px)] blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <div className="relative flex items-center justify-center bg-gray-800/60 text-white px-8 py-4 rounded-[var(--radius-badge)] border border-gray-700/60 backdrop-blur-xl shadow-xl hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 group-hover:border-gray-600/80">
                <Sparkles
                  className="w-5 h-5 mr-3 text-purple-400 animate-pulse"
                  aria-hidden="true"
                />
                <span className="font-semibold tracking-wide text-base bg-clip-text text-transparent bg-gradient-to-r from-purple-300 to-blue-300">
                  Frequently Asked Questions
                </span>
              </div>
            </div>
          </motion.div>

          {/* Enhanced title with better gradients */}
          <motion.h2
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight"
          >
            <span className="block bg-clip-text text-transparent bg-gradient-to-r from-white via-gray-100 to-white">
              Everything About
            </span>
            <motion.span
              className="block mt-3 md:mt-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-indigo-400 to-blue-400"
              animate={{
                backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: 'linear',
              }}
              style={{
                backgroundSize: '200% 200%',
              }}
            >
              InterChat
            </motion.span>
          </motion.h2>

          {/* Enhanced description */}
          <motion.p
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="max-w-4xl mx-auto text-xl text-gray-300 leading-relaxed"
          >
            Everything you need to know about connecting your Discord communities with InterChat.
            Find comprehensive answers to common questions about setup, advanced features, and
            community moderation.
          </motion.p>
        </motion.div>

        {/* Enhanced FAQ accordion container */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative max-w-5xl mx-auto"
        >
          {/* Enhanced animated glow effect */}
          <motion.div
            animate={{
              opacity: [0, 0.4, 0],
              scale: [0.98, 1.02, 0.98],
            }}
            transition={{
              repeat: Infinity,
              duration: 6,
              ease: 'easeInOut',
            }}
            className="absolute inset-0 rounded-[var(--radius-modal)] bg-gradient-to-r from-purple-500/15 via-indigo-500/10 to-blue-500/15 blur-2xl"
          />

          {/* FAQ items container with better spacing */}
          <div className="relative space-y-6" role="region" aria-labelledby="faq-heading">
            {faqs.map((faq, index) => (
              <div
                key={faq.id}
                itemScope
                itemProp="mainEntity"
                itemType="https://schema.org/Question"
              >
                <AccordionItem
                  title={faq.title}
                  content={
                    <div itemScope itemProp="acceptedAnswer" itemType="https://schema.org/Answer">
                      <div itemProp="text">{faq.content}</div>
                    </div>
                  }
                  isOpen={openIndex === index}
                  onToggle={() => toggleAccordion(index)}
                  index={index}
                />
              </div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced call-to-action with improved button design */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-20 lg:mt-24 text-center relative"
        >
          <Button
            variant="ghost"
            className={cn(
              'group/button relative px-10 py-5 rounded-[var(--radius-button)]',
              'bg-gradient-to-r from-gray-800/60 to-gray-700/60 backdrop-blur-xl',
              'border border-gray-600/60 hover:border-purple-500/60',
              'transition-all duration-500',
              'hover:shadow-2xl hover:shadow-purple-500/25 hover:scale-105',
              'focus:outline-none focus:ring-2 focus:ring-purple-500/60 focus:ring-offset-2 focus:ring-offset-gray-900',
            )}
            asChild
          >
            <Link
              href="/docs"
              className="flex items-center justify-center gap-4"
              aria-label="View All InterChat Documentation"
            >
              {/* Button glow effect */}
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-[calc(var(--radius-button)+4px)] opacity-0 group-hover/button:opacity-100 transition-opacity duration-500 blur-sm" />

              <span className="relative font-semibold text-lg bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-blue-400 group-hover/button:from-purple-300 group-hover/button:to-blue-300 transition-all duration-300">
                View All InterChat Documentation
              </span>
              <motion.div
                className="relative p-2 rounded-[var(--radius-button)] bg-gradient-to-r from-purple-500/25 to-blue-500/25 border border-purple-500/40 group-hover/button:from-purple-500/35 group-hover/button:to-blue-500/35 group-hover/button:border-purple-400/60 transition-all duration-300"
                whileHover={{ scale: 1.1, rotate: [0, -5, 5, 0] }}
                whileTap={{ scale: 0.9 }}
              >
                <ArrowRight
                  className="w-4 h-4 text-purple-300 group-hover/button:text-purple-200 transition-all duration-300 transform group-hover/button:translate-x-1"
                  aria-hidden="true"
                />
              </motion.div>
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}

const faqs = [
  {
    id: 'what-is-interchat',
    title: 'What is InterChat?',
    content: (
      <>
        InterChat is a Discord bot that connects a channel in your server with channels from other
        servers. This creates a shared chatroom, called a &quot;hub,&quot; where you can talk with
        other communities in real-time without ever leaving your own server. It&apos;s perfect for
        collaboration, finding new communities, and growing your own.
      </>
    ),
  },
  {
    id: 'how-do-i-get-started',
    title: 'How do I get started with InterChat?',
    content: (
      <>
        Getting started is easy and takes just a couple of minutes. First, invite the bot to your
        server. Then, in the channel you want to connect, run the{' '}
        <code className="px-2 py-1 bg-gray-800/60 rounded text-purple-300">/setup</code> command.
        The bot will guide you through joining an existing hub or creating a new one. That&apos;s
        it! You&apos;ll be connected and chatting with other communities instantly. For more
        advanced guides and a full command list, visit our{' '}
        <Link
          href="/docs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          documentation
        </Link>
        .
      </>
    ),
  },
  {
    id: 'what-is-a-hub',
    title: 'What is an InterChat hub?',
    content: (
      <>
        A hub is the shared chatroom that connects channels from different servers. When you send a
        message in your linked channel, it instantly appears in all other servers connected to that
        same hub. Hubs can be <strong>public</strong>, allowing anyone to find and join them, or{' '}
        <strong>private</strong>, making them invitation-only for complete control. You can explore
        all public hubs using our{' '}
        <Link
          href="/hubs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          Hub Browser
        </Link>{' '}
        to find and connect with new communities.
      </>
    ),
  },
  {
    id: 'is-interchat-free-to-use',
    title: 'Is InterChat free to use?',
    content: (
      <>
        Yes, InterChat is completely free and open-source. All core features are available to
        everyone at no cost. Our goal is to provide powerful communication tools to all communities,
        regardless of size. We are supported by community contributions. If you find InterChat
        valuable, you can help us cover server costs and support development by contributing on
        GitHub or joining our Discord community.
      </>
    ),
  },
  {
    id: 'advanced-features',
    title: 'How do I learn more about InterChat?',
    content: (
      <>
        To master InterChat&apos;s advanced features, the best place to start is our comprehensive{' '}
        <Link
          href="/docs"
          className="text-purple-400 hover:text-purple-300 font-medium underline underline-offset-2 transition-colors duration-200"
        >
          documentation
        </Link>
        . It&apos;s packed with step-by-step tutorials, a full command reference, and best-practice
        guides for moderation. Whether you want to configure automated message filters, customize
        permissions, or analyze hub activity, the docs provide all the information you need to
        unlock InterChat&apos;s full potential.
      </>
    ),
  },
  {
    id: 'moderation-safety',
    title: 'How does InterChat handle moderation and safety?',
    content: (
      <>
        We prioritize your community&apos;s safety with a two-level moderation system. Server admins
        always maintain full moderation control over their own channels using their existing bots
        and rules. Additionally, hub owners can manage behavior across all connected servers.
        InterChat provides built-in tools like customizable word filters, spam detection, and user
        reporting to ensure a safe and positive experience for everyone. Learn more in our dedicated
        safety documentation.
      </>
    ),
  },
];
