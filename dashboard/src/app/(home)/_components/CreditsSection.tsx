import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Github, Linkedin, Globe, Sparkles } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { DiscordIcon } from './DiscordIcon';
import { motion } from 'motion/react';

interface Contributor {
  name: string;
  role: string;
  roleType: 'developer' | 'staff' | 'translator';
  avatar?: string;
  github?: string;
  discord?: string;
  linkedin?: string;
  website?: string;
}

const getRoleBadgeStyles = (roleType: Contributor['roleType']) => {
  switch (roleType) {
    case 'developer':
      return 'bg-purple-600 dark:bg-purple-700 text-white';
    case 'staff':
      return 'bg-emerald-600 dark:bg-emerald-700 text-white';
    case 'translator':
      return 'bg-amber-600 dark:bg-amber-700 text-white';
    default:
      return '';
  }
};

const contributors: Contributor[] = [
  // Developers
  {
    name: 'devoid.',
    role: 'Lead Developer',
    roleType: 'developer',
    avatar:
      'https://cdn.discordapp.com/avatars/701727675311587358/789fb02094d8963727a2cf6bf78c99fe.webp?size=4096',
    github: 'dev-737',
    discord: 'cgYgC6YZyX',
  },
  {
    name: 'devoid.',
    role: 'Developer',
    roleType: 'developer',
    avatar:
      'https://cdn.discordapp.com/avatars/934537337113804891/d9f083f11890342530765b68221a0de1.webp?size=4096',
    github: 'dev-737',
  },
  // Staff
  {
    name: 'hecash',
    role: 'Community Manager',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/1160735837940617336/d21ca7a5f0734334387cd53e8dbd8a38.webp?size=4096',
    discord: '37cjURADY3',
    website: 'https://jetskiiix.straw.page',
  },
  {
    name: 'orange_mitro',
    role: 'Moderator',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/994411851557392434/3a60b2ba79d2e71617334f4eef98ec8c.webp?size=4096',
  },
  {
    name: 'loveitsgood',
    role: 'Bot Moderator',
    roleType: 'staff',
    avatar:
      'https://cdn.discordapp.com/avatars/853178500193583104/9ed1f465b20a1b95c2d960f7ae35ed85.webp?size=4096',
  },
  // Translators
  {
    name: 'spacelemoon',
    role: 'Russian Translator',
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/845357241132384286/954653d2f58cdf003709515df5820a0c.webp?size=4096',
  },
  {
    name: 'dannybarbosabr',
    role: 'Portuguese Translator', // Corrected spelling
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/1067849662347878401/ed4f535c935e9c7d946e9ee8bb57ba06.webp?size=4096',
    discord: 'b4dyWb3wGX',
    github: 'DannyBarbosaBR',
    linkedin: 'daniel-barbosa-de-lima-4181b4266',
  },
  {
    name: 'Chenxian.201277050224',
    role: 'Chinese Translator',
    roleType: 'translator',
  },
  {
    name: 'wakabearhasaname',
    role: 'Hindi Translator',
    avatar:
      'https://cdn.discordapp.com/avatars/1065564110844071996/2add66078f6c8a2908e46f87113ddb3f.webp?size=4096',
    roleType: 'translator',
  },
  {
    name: 'lautydev',
    role: 'Spanish Translator',
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/656842811496333322/de43b1b4de1e91581ee9db3ad9852694.webp?size=4096',
    github: 'LautyDev',
  },
  {
    name: 'tnfangel',
    role: 'Spanish Translator',
    roleType: 'translator',
    avatar:
      'https://cdn.discordapp.com/avatars/******************/e53fd8d7fad3914bbff129f04cbd058d.webp?size=4096',
    github: 'tnfAngel',
    website: 'https://www.tnfangel.com',
  },
];

export const CreditsSection = () => {
  const sortedContributors = [...contributors].sort((a, b) => {
    if (a.roleType === b.roleType) return 0;
    if (a.roleType === 'developer') return -1;
    if (b.roleType === 'developer') return 1;
    if (a.roleType === 'staff') return -1;
    if (b.roleType === 'staff') return 1;
    return 0;
  });

  return (
    <section
      className="relative overflow-hidden py-24 md:py-32 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950"
      id="team"
    >
      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-gray-800/20 via-transparent to-transparent" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary-alt/5 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-6">
            <Sparkles className="h-4 w-4 text-primary animate-pulse" />
            <span className="font-semibold tracking-wide">Meet Our Team</span>
          </div>

          <h2 className="text-4xl md:text-6xl font-bold mb-6 text-white tracking-tight">
            The Talented Team Behind{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary-alt to-primary">
              InterChat&apos;s Success
            </span>
          </h2>

          <p className="max-w-3xl mx-auto text-lg md:text-xl text-gray-300 leading-relaxed">
            Meet the dedicated individuals who contribute their skills and passion to make InterChat
            a reality. From developers to community managers and translators, our team works
            tirelessly to provide you with the best possible cross-server Discord experience.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 relative z-10">
          {sortedContributors.map((contributor, index) => (
            <motion.div
              key={contributor.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group relative"
              itemScope // Added for Schema
              itemType="https://schema.org/Person" // Added for Schema
            >
              {/* Enhanced glow effect */}
              <div className="absolute -inset-1 rounded-[var(--radius-modal)] bg-gradient-to-br from-primary/20 to-primary-alt/20 blur-xl opacity-0 group-hover:opacity-60 transition-opacity duration-500" />

              <div className="relative bg-gray-800/60 backdrop-blur-xl rounded-[var(--radius-modal)] p-6 border border-gray-700/60 group-hover:border-gray-600/70 hover:bg-gray-800/80 hover:shadow-2xl hover:shadow-primary/10 transition-all duration-300 group-hover:scale-[1.02]">
                <div className="flex items-start gap-4">
                  {contributor.avatar ? (
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-primary/30 to-primary-alt/30 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      <Image
                        src={contributor.avatar}
                        alt={`${contributor.name}'s avatar`} // Improved alt text
                        width={64}
                        height={64}
                        className="relative rounded-full border-2 border-gray-700/50 group-hover:border-gray-600/70 transition-colors duration-300"
                        itemProp="image" // Added for Schema
                      />
                    </div>
                  ) : (
                    <div className="w-16 h-16 rounded-full flex items-center justify-center bg-gradient-to-br from-gray-700/60 to-gray-800/60 text-primary border-2 border-gray-600/50 group-hover:border-primary/50 transition-colors duration-300">
                      <span className="text-xl font-bold">{contributor.name[0]}</span>
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <h4
                      className="text-lg font-bold text-white group-hover:text-primary transition-colors duration-300 mb-2"
                      itemProp="name"
                    >
                      {contributor.name}
                    </h4>
                    <Badge
                      className={cn('mb-4', getRoleBadgeStyles(contributor.roleType))}
                      itemProp="jobTitle" // Added for Schema
                    >
                      {contributor.role}
                    </Badge>

                    {/* Social links */}
                    <div className="flex gap-2">
                      {contributor.github && (
                        <Link
                          href={`https://github.com/${contributor.github}?utm_source=interchat.tech&utm_medium=referral`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-[var(--radius)] hover:bg-gray-700/50 transition-colors group/link"
                          aria-label={`View ${contributor.name}'s GitHub profile`}
                        >
                          <Github
                            className="h-4 w-4 text-gray-400 group-hover/link:text-white transition-colors"
                            aria-hidden="true"
                          />
                        </Link>
                      )}
                      {contributor.linkedin && (
                        <Link
                          href={`https://linkedin.com/${contributor.linkedin}?utm_source=interchat.tech&utm_medium=referral`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-[var(--radius)] hover:bg-gray-700/50 transition-colors group/link"
                          aria-label={`View ${contributor.name}'s LinkedIn profile`}
                        >
                          <Linkedin
                            className="h-4 w-4 text-gray-400 group-hover/link:text-white transition-colors"
                            aria-hidden="true"
                          />
                        </Link>
                      )}
                      {contributor.website && (
                        <Link
                          href={`${contributor.website}?utm_source=interchat.tech`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-[var(--radius)] hover:bg-gray-700/50 transition-colors group/link"
                          aria-label={`Visit ${contributor.name}'s website`}
                        >
                          <Globe
                            className="h-4 w-4 text-gray-400 group-hover/link:text-white transition-colors"
                            aria-hidden="true"
                          />
                        </Link>
                      )}
                      {contributor.discord && (
                        <Link
                          href={`https://discord.com/invite/${contributor.discord}?utm_source=interchat.tech&utm_medium=referral`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 rounded-[var(--radius)] hover:bg-gray-700/50 transition-colors group/link"
                          aria-label={`Contact ${contributor.name} on Discord`}
                        >
                          <DiscordIcon
                            className="h-4 w-4 fill-gray-400 group-hover/link:fill-white transition-colors"
                            aria-hidden="true"
                          />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};
