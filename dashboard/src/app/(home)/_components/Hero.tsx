'use client';

import { StaticHeroBackground } from '@/app/(home)/_components/StaticHeroBackground';
import { Button } from '@/components/ui/button';
import { AnimatedShinyText } from '@/components/ui/animated-shiny-text';
import { ArrowRight, LayoutDashboard, Sparkles, Play } from 'lucide-react';
import Link from 'next/link';
import { motion } from 'motion/react';
import { useState } from 'react';

export function Hero() {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <section
      id="hero"
      className="relative overflow-hidden pt-24 md:pt-32 pb-20 md:pb-28 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950"
    >
      <StaticHeroBackground />
      <div className="absolute inset-0 bg-mesh-gradient opacity-20 mix-blend-overlay" />

      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-primary-alt/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="flex flex-col items-center text-center max-w-6xl mx-auto">
          {/* Top Center Text Content */}
          <div className="w-full mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-6 hover:border-gray-600/70 transition-all duration-300"
            >
              <Sparkles className="h-4 w-4 text-primary animate-pulse" />
              <Link href="https://docs.interchat.tech/changelog">
                <AnimatedShinyText className="font-semibold tracking-wide">
                  Introducing InterChat v5
                </AnimatedShinyText>
              </Link>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.1 }}
              className="text-4xl md:text-6xl lg:text-7xl font-extrabold leading-[1.1] tracking-tight bg-clip-text text-transparent bg-gradient-to-br from-white via-gray-100 to-gray-300 mb-6"
            >
              Connect Chats,{' '}
              <span className="relative bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary-alt to-primary">
                Share Conversations.
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8"
            >
              InterChat links Discord servers in real-time, letting messages, media, and
              interactions flow between them. So everyone feels part of the same space, no matter
              where they are.
            </motion.p>

            {/* Enhanced CTAs */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 mb-6 justify-center"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="relative group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-alt rounded-[var(--radius-button)] blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300" />
                <Button
                  size="lg"
                  className="relative h-14 px-8 bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Link href="/invite" className="flex items-center">
                    Add to Discord
                    <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </Button>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="relative group"
              >
                <Button
                  size="lg"
                  variant="outline"
                  className="h-14 px-8 border-gray-600/70 bg-gray-800/60 backdrop-blur-xl hover:bg-gray-800/80 hover:border-gray-500/70 text-white font-semibold shadow-lg transition-all duration-300"
                >
                  <Link href="/dashboard" className="flex items-center">
                    Explore Dashboard
                    <LayoutDashboard className="ml-3 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>

            {/* Enhanced Subtext */}
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="text-sm text-gray-400 max-w-lg mx-auto leading-relaxed"
            >
              No setup hassle. Keep full moderation control while you connect communities across
              servers.
              <span className="text-gray-300 font-medium">
                {' '}
                Join 11,000+ servers already connected.
              </span>
            </motion.p>
          </div>

          {/* Video Section - Full Glory Below Text */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="relative w-full max-w-5xl mx-auto"
          >
            {/* Enhanced decorative glow */}
            <div className="absolute -inset-4 rounded-[var(--radius-modal)] bg-gradient-to-r from-primary/40 via-primary-alt/40 to-primary/40 blur-3xl opacity-20 group-hover:opacity-40 transition-opacity duration-500" />

            <div className="relative rounded-[var(--radius-modal)] border border-gray-700/60 bg-gradient-to-br from-gray-900/90 to-gray-950/90 shadow-2xl overflow-hidden backdrop-blur-xl">
              {/* Video Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-700/50 bg-gray-900/80">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 rounded-full bg-gradient-to-r from-primary to-primary-alt flex items-center justify-center">
                    <Play className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-gray-200 font-medium">
                    InterChat in Action (Coming Soon)
                  </span>
                </div>
              </div>

              {/* Video Container */}
              <div className="relative aspect-video bg-gray-900/50">
                {/* Placeholder for video - you can replace this with your actual video */}
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-800/80 to-gray-900/80">
                  {!isVideoPlaying ? (
                    <motion.button
                      onClick={() => setIsVideoPlaying(true)}
                      className="group relative"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-alt rounded-full blur-xl opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
                      <div className="relative w-20 h-20 md:w-24 md:h-24 bg-gradient-to-r from-primary to-primary-alt rounded-full flex items-center justify-center shadow-2xl border border-white/20">
                        <Play className="w-8 h-8 md:w-10 md:h-10 text-white ml-1" />
                      </div>
                    </motion.button>
                  ) : (
                    /* Replace this div with your actual video element */
                    <div className="w-full h-full flex items-center justify-center text-white">
                      {/* Video element would go here */}
                      <video
                        className="w-full h-full object-cover"
                        controls
                        autoPlay
                        poster="/path-to-your-video-poster.jpg"
                      >
                        <source src="/path-to-your-video.mp4" type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                  )}
                </div>

                {/* Video Overlay Text (appears over video) */}
                {!isVideoPlaying && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                    className="absolute bottom-6 left-6 right-6"
                  >
                    <div className="rounded-[var(--radius)] border border-primary/30 bg-gradient-to-r from-primary/10 to-primary-alt/10 p-4 text-sm text-gray-300 backdrop-blur-sm">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/20 border border-primary/30">
                          <Sparkles className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <div className="text-xs text-gray-400">
                            Watch how cross-server communication works
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
