import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, Code, Github, GitFork, Heart, Star, Users } from "lucide-react";
import Link from "next/link";

export function OpenSourceSection() {
  return (
    <section className="relative py-20 md:py-32 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary-alt/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent" />
      </div>


      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-20 relative z-10">
          <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-6">
            <Code className="h-4 w-4 text-primary animate-pulse" />
            <span className="font-semibold tracking-wide">Open Source</span>
          </div>

          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">
            Built by the Community,
            <span className="block mt-3 text-primary">
              For the Community
            </span>
          </h2>
          <p className="max-w-3xl mx-auto text-lg text-gray-300">
            InterChat is completely open source and free to use. Join our growing community of contributors
            and help shape the future of Discord cross-server communication.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16 relative z-10">
          {/* GitHub Repository Card */}
          <div className="group relative">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 to-primary-alt/10 blur-xl opacity-0 group-hover:opacity-50 transition-opacity duration-700" />

            <div className="bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 group-hover:border-gray-600/70 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 relative h-full">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 rounded-full bg-gray-700/50 border border-gray-600/50">
                  <Github className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">GitHub Repository</h3>
                  <p className="text-gray-400">Explore the source code</p>
                </div>
              </div>

              <p className="text-gray-300 mb-6">
                Dive into InterChat&apos;s codebase, report issues, suggest features, and contribute to making
                cross-server communication better for everyone.
              </p>

              <div className="flex flex-wrap gap-3 mb-6">
                <Badge className="bg-gray-700/50 text-gray-300 border-gray-600/50">
                  <Star className="w-3 h-3 mr-1" />
                  25+ Stars
                </Badge>
                <Badge className="bg-gray-700/50 text-gray-300 border-gray-600/50">
                  <GitFork className="w-3 h-3 mr-1" />
                  4+ Forks
                </Badge>
                <Badge className="bg-gray-700/50 text-gray-300 border-gray-600/50">
                  <Users className="w-3 h-3 mr-1" />
                  8+ Contributors
                </Badge>
              </div>

              <Button
                asChild
                className="w-full bg-gray-700/50 hover:bg-gray-600/50 text-white border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300"
              >
                <Link
                  href="https://github.com/interchatapp/InterChat"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2"
                >
                  View on GitHub
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </div>

          {/* Contribution Guidelines Card */}
          <div className="group relative">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-primary/10 to-primary-alt/10 blur-xl opacity-0 group-hover:opacity-50 transition-opacity duration-700" />

            <div className="bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 group-hover:border-gray-600/70 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 relative h-full">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 rounded-full bg-gray-700/50 border border-gray-600/50">
                  <Heart className="w-8 h-8 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">Get Involved</h3>
                  <p className="text-gray-400">Join our community</p>
                </div>
              </div>

              <p className="text-gray-300 mb-6">
                Whether you&apos;re a developer, designer, translator, or just passionate about Discord communities,
                there are many ways to contribute to InterChat.
              </p>

              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Report bugs and suggest features</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Contribute code and documentation</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Help with translations</span>
                </div>
                <div className="flex items-center gap-3 text-gray-300">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Join our Discord community</span>
                </div>
              </div>

              <Button
                asChild
                className="w-full bg-primary hover:bg-primary-alt text-white transition-all duration-300"
              >
                <Link
                  href="https://discord.gg/interchat"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2"
                >
                  Join Discord
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
