'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  ArrowRight,
  CheckCircle,
  MessageSquare,
  Settings,
  Shield,
  Sparkles,
  Star,
  Zap,
} from 'lucide-react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';

const features = [
  {
    id: 'cross-server',
    title: 'Cross-Server Communication',
    description:
      'Connect Discord servers with our advanced hub system. Messages flow instantly between connected channels while maintaining server identity.',
    icon: MessageSquare,
    color: 'from-emerald-500/20 to-green-500/20',
    borderColor: 'border-emerald-500/30',
    benefits: [
      'Instant message delivery',
      'Server identity preservation',
      'Unlimited connections',
      'Rich media support',
    ],
    mockup: '/features/CrossChat.svg', // Placeholder for now
    isReversed: false,
  },
  {
    id: 'moderation',
    title: 'Advanced Moderation',
    color: 'from-blue-500/20 to-cyan-500/20',
    borderColor: 'border-blue-500/30',
    description:
      'Keep your communities safe with powerful moderation tools. Hub-specific and global controls ensure the right balance of openness and security.',
    icon: Shield,
    benefits: ['Hub-specific bans', 'Global blacklists', 'Auto-moderation', 'Detailed logging'],
    mockup: '/features/NSFWDetection.svg',
    isReversed: true,
  },
  {
    id: 'performance',
    title: 'Lightning Fast Performance',
    description:
      'Built from the ground up for speed. Our v5 architecture delivers messages faster with improved reliability and reduced latency.',
    icon: Zap,
    color: 'from-yellow-500/20 to-orange-500/20',
    borderColor: 'border-yellow-500/30',
    benefits: ['Optimized infrastructure', 'Reduced latency', '99.99% uptime'],
    mockup: '/mockups/performance-stats.png', // Placeholder for now
    isReversed: false,
  },
  {
    id: 'dashboard',
    title: 'Modern Dashboard',
    description:
      'Manage your hubs and connections with our beautifully redesigned dashboard. Intuitive controls at your fingertips.',
    icon: Settings,
    color: 'from-purple-500/20 to-pink-500/20',
    borderColor: 'border-purple-500/30',
    benefits: [
      'Real-time analytics',
      'Easy hub management',
      'Mobile responsive',
      'Dark mode optimized',
    ],
    mockup: '/features/dashboard.svg',
    isReversed: true,
  },
];

export function FeaturesShowcase() {
  return (
    <section className="relative py-20 md:py-32 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary-alt/5 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-6">
            <Sparkles className="h-4 w-4 text-primary animate-pulse" />
            <span className="font-semibold tracking-wide">Powerful Features</span>
          </div>

          <h2 className="text-4xl md:text-6xl font-bold text-white tracking-tight mb-6">
            Everything you need for
            <span className="block bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary-alt to-primary">
              Cross-Server Chatting
            </span>
          </h2>

          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            InterChat v5 combines cutting-edge technology with intuitive design to deliver the
            ultimate cross-server Discord experience.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="space-y-24 md:space-y-32">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              className={`grid lg:grid-cols-2 gap-12 lg:gap-16 items-center ${
                feature.isReversed ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Content */}
              <div className={feature.isReversed ? 'lg:col-start-2' : ''}>
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <div
                      className={`inline-flex items-center justify-center w-12 h-12 rounded-[var(--radius)] bg-gradient-to-br ${feature.color} border ${feature.borderColor} backdrop-blur-xl`}
                    >
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl md:text-3xl font-bold text-white">{feature.title}</h3>
                  </div>

                  <p className="text-lg text-gray-300 leading-relaxed">{feature.description}</p>

                  <div className="grid grid-cols-2 gap-3">
                    {feature.benefits.map((benefit) => (
                      <div key={benefit} className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-emerald-400 flex-shrink-0" />
                        <span className="text-sm text-gray-300">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Visual/Mockup */}

              <div className={feature.isReversed ? 'lg:col-start-1' : ''}>
                <div className="relative group">
                  <div
                    className={`absolute -inset-4 bg-gradient-to-r ${feature.color} rounded-[var(--radius-modal)] blur-2xl opacity-20 group-hover:opacity-40 transition-opacity duration-500`}
                  />
                  <Card className="relative overflow-hidden rounded-[var(--radius-modal)] border-gray-700/60 bg-gray-800/40 backdrop-blur-xl shadow-2xl">
                    <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
                      {
                        <Image
                          src={feature.mockup}
                          alt={feature.title}
                          fill
                          className="object-cover"
                          sizes="(min-width: 1024px) 800px, 100vw"
                        />
                      }
                    </div>
                  </Card>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
          className="text-center mt-24 md:mt-32"
        >
          <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-6">
            <Star className="h-4 w-4 text-yellow-400" />
            <span className="font-semibold tracking-wide">Ready to get started?</span>
          </div>

          <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Join thousands of communities using InterChat
          </h3>

          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Set up cross-server communication in minutes. No complex configuration required.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                size="lg"
                className="h-12 px-8 bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-semibold shadow-lg"
              >
                <Link href="/invite" className="flex items-center">
                  Add to Discord
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                size="lg"
                variant="outline"
                className="h-12 px-8 border-gray-600/70 bg-gray-800/60 backdrop-blur-xl hover:bg-gray-800/80 text-white font-semibold"
              >
                <Link href="/docs/getting-started" className="flex items-center">
                  View Documentation
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
