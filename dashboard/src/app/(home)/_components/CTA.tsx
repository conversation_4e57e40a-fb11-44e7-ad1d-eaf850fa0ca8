'use client';

import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { Arrow<PERSON><PERSON>, <PERSON>ap, Sparkles, Users, Home } from 'lucide-react';
import { motion } from 'motion/react';

export function CTA() {
  return (
    <section className="relative py-20 md:py-32 bg-gradient-to-b from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary-alt/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/5 via-transparent to-transparent" />
      </div>

      <div className="container mx-auto px-4 text-center relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.7 }}
          className="max-w-4xl mx-auto"
        >
          {/* Enhanced badge */}
          <div className="inline-flex items-center gap-2 rounded-[var(--radius-button)] border border-gray-700/60 bg-gradient-to-r from-gray-800/60 to-gray-800/40 backdrop-blur-xl px-4 py-2 text-sm text-gray-300 shadow-lg mb-8">
            <Zap className="h-4 w-4 text-emerald-400 animate-pulse" />
            <span className="font-semibold tracking-wide">Fast, clean, and easy to use</span>
          </div>

          {/* Enhanced heading */}
          <h3 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white tracking-tight mb-6">
            Get started with{' '}
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary-alt to-primary">
              InterChat
            </span>
          </h3>

          {/* Enhanced description */}
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-10">
            Connect your Discord servers in minutes. Explore public hubs or build your own community
            network.
            <span className="block mt-2 text-gray-400">
              Join thousands of communities already using InterChat to bridge their servers.
            </span>
          </p>

          {/* Stats row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex flex-wrap justify-center gap-8 mb-12"
          >
            {[
              { icon: Users, label: '12,000+', sublabel: 'Connected Servers' },
              { icon: Home, label: '30+', sublabel: 'Hubs Chatting Now' },
              { icon: Zap, label: '99.9%', sublabel: 'Uptime' },
            ].map((stat) => (
              <div key={stat.label} className="flex items-center gap-3">
                <div className="p-2 rounded-[var(--radius)] bg-gray-800/60 border border-gray-700/50">
                  <stat.icon className="h-5 w-5 text-primary" />
                </div>
                <div className="text-left">
                  <div className="text-xl font-bold text-white">{stat.label}</div>
                  <div className="text-sm text-gray-400">{stat.sublabel}</div>
                </div>
              </div>
            ))}
          </motion.div>

          {/* Enhanced CTAs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-primary to-primary-alt rounded-[var(--radius-button)] blur-lg opacity-0 group-hover:opacity-30 transition-opacity duration-300" />
              <Button
                asChild
                size="lg"
                className="relative h-14 px-8 bg-gradient-to-r from-primary to-primary-alt hover:from-primary-alt hover:to-primary text-white font-semibold shadow-lg hover:shadow-xl"
              >
                <Link href="/hubs" className="flex items-center group">
                  <Sparkles className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform duration-300" />
                  Discover Hubs
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="h-14 px-8 border-gray-600/70 bg-gray-800/60 backdrop-blur-xl hover:bg-gray-800/80 hover:border-gray-500/70 text-white font-semibold shadow-lg"
              >
                <Link href="/docs/getting-started" className="flex items-center group">
                  Learn how it works
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </motion.div>
          </motion.div>

          {/* Trust indicators */}
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-8 text-sm text-gray-400"
          >
            Trusted by communities worldwide • Free to use • Simple Setup
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}
