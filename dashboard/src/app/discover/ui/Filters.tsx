'use client';

import { useMemo } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { SUPPORTED_LANGUAGES } from '@/lib/constants';
import { TagPicker } from '@/components/discover/TagPicker';

export type FeatureFlags = { verified?: boolean; partnered?: boolean; nsfw?: boolean };

export function Filters(props: {
  q: string;
  onQChange: (v: string) => void;
  sort: 'trending' | 'active' | 'new' | 'upvoted';
  onSortChange: (v: 'trending' | 'active' | 'new' | 'upvoted') => void;

  tags: string[];
  onTagsChange: (tags: string[]) => void;

  language?: string;
  onLanguageChange: (lang?: string) => void;

  region?: string;
  onRegionChange: (region?: string) => void;

  activity: ('LOW' | 'MEDIUM' | 'HIGH')[];
  onActivityChange: (values: ('LOW' | 'MEDIUM' | 'HIGH')[]) => void;

  features: FeatureFlags;
  onFeaturesChange: (f: FeatureFlags) => void;
}) {
  const languageOptions = useMemo(() => SUPPORTED_LANGUAGES, []);

  return (
    <aside className="premium-card rounded-[var(--radius)] p-6 space-y-6">
      <div className="flex items-center gap-2 pb-2 border-b border-gray-700/30">
        <h2 className="text-sm font-semibold text-white">Filters</h2>
      </div>

      <div className="flex flex-col gap-6">
        {/* Sort */}
        <div className="flex flex-col gap-3">
          <label className="text-xs font-medium text-gray-300 uppercase tracking-wide">
            Sort by
          </label>
          <Select
            value={props.sort}
            onValueChange={(v: string) =>
              props.onSortChange(v as 'trending' | 'active' | 'new' | 'upvoted')
            }
          >
            <SelectTrigger className="select-standard">
              <SelectValue placeholder="Sort" />
            </SelectTrigger>
            <SelectContent className="select-content">
              <SelectItem value="trending">🔥 Trending</SelectItem>
              <SelectItem value="active">⚡ Most Active</SelectItem>
              <SelectItem value="new">✨ Newest</SelectItem>
              <SelectItem value="upvoted">👍 Most Upvoted</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <TagPicker selectedTags={props.tags} onTagsChange={props.onTagsChange} maxTags={5} />

        {/* Language */}
        <div className="flex flex-col gap-3">
          <label className="text-xs font-medium text-gray-300 uppercase tracking-wide">
            Language
          </label>
          <Select
            value={props.language ?? 'any'}
            onValueChange={(v) => props.onLanguageChange(v === 'any' ? undefined : v)}
          >
            <SelectTrigger className="select-standard">
              <SelectValue placeholder="Any language" />
            </SelectTrigger>
            <SelectContent className="select-content">
              <SelectItem value="any">Any language</SelectItem>
              {languageOptions.map((l) => (
                <SelectItem key={l.code} value={l.code}>
                  {l.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {/* Activity */}
        <div className="flex flex-col gap-3">
          <label className="text-xs font-medium text-gray-300 uppercase tracking-wide">
            Activity Level
          </label>
          <div className="grid grid-cols-1 gap-2">
            {(['LOW', 'MEDIUM', 'HIGH'] as const).map((lvl) => {
              const checked = props.activity.includes(lvl);
              const activityIcons = { LOW: '🐢', MEDIUM: '🚶', HIGH: '🚀' };
              const activityColors = {
                LOW: 'text-amber-300 border-amber-500/30 bg-amber-500/10',
                MEDIUM: 'text-blue-300 border-blue-500/30 bg-blue-500/10',
                HIGH: 'text-emerald-300 border-emerald-500/30 bg-emerald-500/10',
              };
              return (
                <label
                  key={lvl}
                  className={`inline-flex items-center gap-3 p-3 rounded-[var(--radius-button)] border transition-all duration-200 cursor-pointer hover:bg-gray-800/50 ${
                    checked
                      ? activityColors[lvl]
                      : 'border-gray-700/50 bg-gray-900/30 text-gray-300 hover:border-gray-600/50'
                  }`}
                >
                  <Checkbox
                    checked={checked}
                    onCheckedChange={(val) => {
                      const on = Boolean(val);
                      let next = props.activity;
                      if (on && !next.includes(lvl)) next = [...next, lvl];
                      if (!on) next = next.filter((x) => x !== lvl);
                      props.onActivityChange(next);
                    }}
                    className="data-[state=checked]:bg-current data-[state=checked]:border-current"
                  />
                  <span className="text-sm">{activityIcons[lvl]}</span>
                  <span className="text-sm font-medium capitalize">{lvl.toLowerCase()}</span>
                </label>
              );
            })}
          </div>
        </div>

        {/* Features */}
        <div className="flex flex-col gap-3">
          <label className="text-xs font-medium text-gray-300 uppercase tracking-wide">
            Features
          </label>
          <div className="grid grid-cols-1 gap-2">
            <label className="inline-flex items-center gap-3 p-3 rounded-[var(--radius-button)] border transition-all duration-200 cursor-pointer hover:bg-gray-800/50 border-gray-700/50 bg-gray-900/30">
              <Checkbox
                checked={!!props.features.verified}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({ ...props.features, verified: Boolean(v) })
                }
                className="data-[state=checked]:bg-emerald-500 data-[state=checked]:border-emerald-500"
              />
              <span className="text-sm">✅</span>
              <span className="text-sm font-medium text-gray-200">Verified</span>
            </label>
            <label className="inline-flex items-center gap-3 p-3 rounded-[var(--radius-button)] border transition-all duration-200 cursor-pointer hover:bg-gray-800/50 border-gray-700/50 bg-gray-900/30">
              <Checkbox
                checked={!!props.features.partnered}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({ ...props.features, partnered: Boolean(v) })
                }
                className="data-[state=checked]:bg-indigo-500 data-[state=checked]:border-indigo-500"
              />
              <span className="text-sm">🤝</span>
              <span className="text-sm font-medium text-gray-200">Partnered</span>
            </label>
            <label className="inline-flex items-center gap-3 p-3 rounded-[var(--radius-button)] border transition-all duration-200 cursor-pointer hover:bg-gray-800/50 border-gray-700/50 bg-gray-900/30">
              <Checkbox
                checked={!!props.features.nsfw}
                onCheckedChange={(v) =>
                  props.onFeaturesChange({ ...props.features, nsfw: Boolean(v) })
                }
                className="data-[state=checked]:bg-red-500 data-[state=checked]:border-red-500"
              />
              <span className="text-sm">🔞</span>
              <span className="text-sm font-medium text-gray-200">NSFW</span>
            </label>
          </div>
        </div>
      </div>
    </aside>
  );
}
