'use client';

import { useEffect } from 'react';
import { signIn } from '@/lib/auth-client';
import { useSearchParams } from 'next/navigation';

export default function LoginPage() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/';

  useEffect(() => {
    // Automatically trigger Discord OAuth on page load
    signIn.social({
      provider: 'discord',
      callbackURL: callbackUrl,
    });
  }, [callbackUrl]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="mx-auto max-w-sm space-y-6 p-6 text-center">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Redirecting to Discord...</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Please wait while we redirect you to Discord for authentication.
          </p>
        </div>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto"></div>
      </div>
    </div>
  );
}
