'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Search, X, Tag, Plus, Sparkles } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useTagSearch, usePopularTags } from '@/hooks/use-tags';

interface TagSelectorProps {
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  placeholder?: string;
  maxTags?: number;
  showPopular?: boolean;
  allowTagCreation?: boolean; // New prop to control tag creation
}

/**
 * Enhanced Tag Selector Component
 * Features autocomplete, popular tags, and smooth animations
 */
export function TagSelector({
  selectedTags,
  onTagsChange,
  placeholder = 'Search for tags...',
  maxTags = 5,
  showPopular = true,
  allowTagCreation = true, // Default to true for backward compatibility
}: TagSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Hooks for tag data
  const { tags: searchResults, isLoading: isSearching } = useTagSearch(searchQuery);
  const { tags: popularTags, isLoading: isLoadingPopular } = usePopularTags(12);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    const availableTags = searchQuery ? searchResults : popularTags;
    const filteredTags = availableTags.filter((tag) => !selectedTags.includes(tag.name));

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex((prev) => Math.min(prev + 1, filteredTags.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex((prev) => Math.max(prev - 1, -1));
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && filteredTags[focusedIndex]) {
          addTag(filteredTags[focusedIndex].name);
        } else if (searchQuery.trim() && allowTagCreation) {
          addTag(searchQuery.trim());
        }
        break;
      case 'Escape':
        setIsOpen(false);
        inputRef.current?.blur();
        break;
      case 'Backspace':
        if (!searchQuery && selectedTags.length > 0) {
          removeTag(selectedTags[selectedTags.length - 1]);
        }
        break;
    }
  };

  const addTag = (tagName: string) => {
    const normalizedTag = tagName.trim();
    if (normalizedTag && !selectedTags.includes(normalizedTag) && selectedTags.length < maxTags) {
      onTagsChange([...selectedTags, normalizedTag]);
      setSearchQuery('');
      setFocusedIndex(-1);
      inputRef.current?.focus();
    }
  };

  const removeTag = (tagName: string) => {
    onTagsChange(selectedTags.filter((tag) => tag !== tagName));
  };

  const clearAllTags = () => {
    onTagsChange([]);
  };

  // Get tags to display in dropdown
  const getDisplayTags = () => {
    const availableTags = searchQuery ? searchResults : popularTags;
    return availableTags.filter((tag) => !selectedTags.includes(tag.name));
  };

  const displayTags = getDisplayTags();
  const canAddMore = selectedTags.length < maxTags;

  return (
    <div className="relative">
      {/* Selected Tags */}
      {selectedTags.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-300">
              Selected Tags ({selectedTags.length}/{maxTags})
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllTags}
              className="text-gray-400 hover:text-white h-auto p-1"
            >
              Clear All
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedTags.map((tag) => (
              <motion.div
                key={tag}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <Badge
                  variant="secondary"
                  className="bg-blue-600/20 text-blue-300 border-blue-600/30 pr-1 group hover:bg-blue-600/30 transition-colors"
                >
                  <Tag className="w-3 h-3 mr-1" />
                  {tag}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeTag(tag)}
                    className="ml-1 h-auto p-0 text-blue-300 hover:text-white group-hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </Badge>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Search Input */}
      <div className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            ref={inputRef}
            type="text"
            placeholder={canAddMore ? placeholder : `Maximum ${maxTags} tags selected`}
            value={searchQuery}
            onChange={(e) => {
              setSearchQuery(e.target.value);
              setIsOpen(true);
              setFocusedIndex(-1);
            }}
            onFocus={() => setIsOpen(true)}
            onKeyDown={handleKeyDown}
            disabled={!canAddMore}
            className="pl-10 bg-gray-800 border-gray-600 focus:border-blue-500 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchQuery('');
                setFocusedIndex(-1);
                inputRef.current?.focus();
              }}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1 text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Dropdown */}
        <AnimatePresence>
          {isOpen && canAddMore && (
            <motion.div
              ref={dropdownRef}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 z-[9999]"
            >
              <Card className="bg-gray-800 border-gray-700 shadow-xl max-h-64 overflow-hidden relative z-[9999]">
                <CardContent className="p-0">
                  {/* Custom tag creation - only show if allowed */}
                  {allowTagCreation &&
                    searchQuery &&
                    !displayTags.some(
                      (tag) => tag.name.toLowerCase() === searchQuery.toLowerCase(),
                    ) && (
                      <button
                        onClick={() => addTag(searchQuery)}
                        className={`w-full px-4 py-3 text-left hover:bg-gray-700 transition-colors border-b border-gray-700 ${
                          focusedIndex === -1 ? 'bg-gray-700' : ''
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <Plus className="w-4 h-4 text-green-400" />
                          <span className="text-white">Create tag: </span>
                          <Badge variant="outline" className="border-green-400 text-green-400">
                            {searchQuery}
                          </Badge>
                        </div>
                      </button>
                    )}

                  {/* Search results or popular tags */}
                  <div className="max-h-48 overflow-y-auto">
                    {isSearching || isLoadingPopular ? (
                      <div className="p-4 text-center text-gray-400">
                        <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2" />
                        Loading tags...
                      </div>
                    ) : displayTags.length > 0 ? (
                      <>
                        {!searchQuery && showPopular && (
                          <div className="px-4 py-2 border-b border-gray-700">
                            <div className="flex items-center gap-2 text-sm text-gray-400">
                              <Sparkles className="w-4 h-4" />
                              Popular Tags
                            </div>
                          </div>
                        )}
                        {displayTags.map((tag, index) => (
                          <button
                            key={tag.name}
                            onClick={() => addTag(tag.name)}
                            className={`w-full px-4 py-3 text-left hover:bg-gray-700 transition-colors ${
                              index === focusedIndex ? 'bg-gray-700' : ''
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Tag className="w-4 h-4 text-gray-400" />
                                <span className="text-white">{tag.name}</span>
                                {'category' in tag && tag.category && (
                                  <Badge
                                    variant="outline"
                                    className="text-xs border-gray-600 text-gray-400"
                                  >
                                    {tag.category}
                                  </Badge>
                                )}
                              </div>
                              {'usageCount' in tag && tag.usageCount && (
                                <span className="text-xs text-gray-500">{tag.usageCount} uses</span>
                              )}
                            </div>
                          </button>
                        ))}
                      </>
                    ) : (
                      <div className="p-4 text-center text-gray-400">
                        {searchQuery ? 'No tags found' : 'No popular tags available'}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Help Text */}
      <div className="mt-2 text-xs text-gray-500">
        {canAddMore ? (
          allowTagCreation ? (
            <>Press Enter to add a tag, or select from the dropdown</>
          ) : (
            <>Select from existing tags</>
          )
        ) : (
          <>Maximum {maxTags} tags reached</>
        )}
      </div>
    </div>
  );
}
