'use client';
import { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { HubCardDTO } from '@/lib/discover/query';
import { useTRPC } from '@/utils/trpc';
import { useDebounce } from '@/hooks/use-debounce';

import { useQueryClient } from '@tanstack/react-query';

interface HubSearchProps {
  onSearchSubmit?: (query: string) => void;
  className?: string;
}

export function HubSearch({ onSearchSubmit, className }: HubSearchProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState<HubCardDTO[]>([]);
  const [loading, setLoading] = useState(false);
  const debouncedSearch = useDebounce(searchValue, 300);
  const inputRef = useRef<HTMLInputElement>(null);

  // Search function
  const queryClient = useQueryClient();
  const trpc = useTRPC();
  const searchHubs = useCallback(
    async (query: string) => {
      if (!query.trim()) {
        setSearchResults([]);
        return;
      }

      setLoading(true);
      try {
        const data = await queryClient.fetchQuery(
          trpc.discover.list.queryOptions({ q: query, pageSize: 8 }),
        );
        setSearchResults(data.items || []);
      } catch (error) {
        console.error('Search error:', error);
        setSearchResults([]);
      } finally {
        setLoading(false);
      }
    },
    [queryClient, trpc.discover.list],
  );

  // Effect for debounced search
  useEffect(() => {
    if (debouncedSearch) {
      searchHubs(debouncedSearch);
    } else {
      setSearchResults([]);
    }
  }, [debouncedSearch, searchHubs]);

  // Handle search submission
  const handleSearchSubmit = () => {
    if (searchValue.trim() && onSearchSubmit) {
      onSearchSubmit(searchValue.trim());
      setOpen(false);
      setSearchValue('');
      setSearchResults([]);
    }
  };

  // Handle Enter key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSearchSubmit();
    }
    if (e.key === 'Escape') {
      setOpen(false);
    }
  };

  // Auto-focus input when popover opens
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [open]);

  // Global keyboard shortcut handler
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if (e.key === '/' && !open && e.target === document.body) {
        e.preventDefault();
        setOpen(true);
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, [open]);

  return (
    <div className={className}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            role="button"
            tabIndex={0}
            aria-expanded={open}
            className="w-full cursor-pointer premium-card border border-gray-700/50 hover:border-purple-500/50 bg-gray-900/50 hover:bg-gray-800/50 transition-all duration-300 h-14 text-base px-4 shadow-lg hover:shadow-xl rounded-[var(--radius-button)] flex items-center"
            onClick={() => setOpen(!open)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setOpen(!open);
              }
            }}
          >
            <Search className="mr-3 h-5 w-5 text-gray-400 flex-shrink-0" />
            <span className="text-gray-400 flex-1 text-left">
              {searchValue || 'Search for communities, topics, or interests...'}
            </span>
            {searchValue && (
              <div
                className="ml-auto h-6 w-6 p-0 hover:bg-gray-700/50 flex-shrink-0 rounded flex items-center justify-center cursor-pointer transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setSearchValue('');
                  setSearchResults([]);
                }}
              >
                <X className="h-3 w-3" />
              </div>
            )}
            <div className="ml-2 flex-shrink-0 text-xs text-gray-500 hidden sm:block">
              Press / to search
            </div>
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[var(--radix-popover-trigger-width)] min-w-[600px] p-0 bg-gray-900/95 border-gray-600/50"
          align="start"
          sideOffset={8}
        >
          <Command className="bg-transparent border-none">
            <CommandInput
              ref={inputRef}
              placeholder="Search for communities, topics, or interests..."
              value={searchValue}
              onValueChange={setSearchValue}
              onKeyDown={handleKeyDown}
              className="border-none bg-transparent focus:ring-0 text-base h-12 text-white placeholder:text-gray-500"
            />
            <CommandList className="max-h-[400px]">
              {loading && (
                <div className="flex items-center justify-center py-6">
                  <div className="flex items-center gap-2 text-gray-400">
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-600 border-t-purple-400"></div>
                    <span>Searching...</span>
                  </div>
                </div>
              )}

              {!loading && searchValue && searchResults.length === 0 && (
                <CommandEmpty className="py-6 text-center">
                  <div className="text-gray-400">
                    <Search className="mx-auto h-12 w-12 mb-2 opacity-50" />
                    <p className="text-sm">No communities found for &ldquo;{searchValue}&rdquo;</p>
                    <p className="text-xs mt-1 text-gray-500">
                      Try different keywords or check your spelling
                    </p>
                  </div>
                </CommandEmpty>
              )}

              {searchResults.length > 0 && (
                <>
                  <CommandGroup heading="Communities" className="p-2">
                    {searchResults.map((hub) => (
                      <CommandItem
                        key={hub.id}
                        value={hub.name}
                        onSelect={() => {
                          setOpen(false);
                          setSearchValue('');
                        }}
                        className="p-0 data-[selected=true]:bg-gray-800/50"
                      >
                        <Link
                          href={`/hubs/${hub.id}`}
                          className="flex items-center gap-3 w-full p-3 hover:bg-gray-800/30 rounded-lg transition-colors"
                        >
                          <Avatar className="h-10 w-10 ring-1 ring-gray-700/50">
                            <AvatarImage
                              src={hub.iconUrl || '/default-server.svg'}
                              alt={hub.name}
                            />
                            <AvatarFallback className="bg-gray-800 text-white text-sm">
                              {hub.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium text-white truncate">{hub.name}</h4>
                              {hub.verified && (
                                <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                                  <svg
                                    className="w-2.5 h-2.5 text-white"
                                    fill="currentColor"
                                    viewBox="0 0 20 20"
                                  >
                                    <path
                                      fillRule="evenodd"
                                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                      clipRule="evenodd"
                                    />
                                  </svg>
                                </div>
                              )}
                              {hub.partnered && (
                                <div className="px-1.5 py-0.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded text-xs font-medium text-white">
                                  Partner
                                </div>
                              )}
                              {hub.nsfw && (
                                <div className="px-1.5 py-0.5 bg-red-500/20 border border-red-500/50 rounded text-xs font-medium text-red-400">
                                  🔞
                                </div>
                              )}
                            </div>
                            <p className="text-sm text-gray-400 truncate mt-0.5">
                              {hub.description ||
                                'A community space for meaningful discussions and connections.'}
                            </p>
                            <div className="flex items-center gap-3 mt-1 text-xs text-gray-500">
                              <span>
                                {hub.weeklyMessageCount?.toLocaleString() ?? '0'} msgs/week
                              </span>
                              <span>•</span>
                              <span>{hub._count.upvotes?.toLocaleString() ?? '0'} members</span>
                              {hub.tags.length > 0 && (
                                <>
                                  <span>•</span>
                                  <span className="truncate">
                                    #
                                    {hub.tags
                                      .slice(0, 2)
                                      .map((tag) => tag.name)
                                      .join(', #')}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                        </Link>
                      </CommandItem>
                    ))}
                  </CommandGroup>

                  {searchValue.trim() && (
                    <CommandGroup className="border-t border-gray-700/50 p-2">
                      <CommandItem
                        value={`search-all-${searchValue}`}
                        onSelect={handleSearchSubmit}
                        className="data-[selected=true]:bg-purple-500/10 hover:bg-purple-500/10"
                      >
                        <div className="flex items-center gap-3 w-full p-2">
                          <div className="h-8 w-8 rounded-full bg-purple-500/20 flex items-center justify-center">
                            <Search className="h-4 w-4 text-purple-400" />
                          </div>
                          <span className="text-purple-400 font-medium">
                            Search all communities for &ldquo;{searchValue}&rdquo;
                          </span>
                        </div>
                      </CommandItem>
                    </CommandGroup>
                  )}
                </>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
