import Image from 'next/image';
import Link from 'next/link';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Users, Heart, Eye, MessageCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { HubCardDTO } from '@/lib/discover/query';
import { useDiscoverUpvote } from '@/hooks/use-discover-upvote';

interface DiscoverHubCardProps extends HubCardDTO {
  onTagClick?: (tagName: string) => void;
}

export default function DiscoverHubCard({
  id,
  name,
  description,
  iconUrl,
  bannerUrl,
  verified,
  partnered,
  tags,
  nsfw,
  weeklyMessageCount,
  activityLevel,
  _count,
  isUpvoted = false,
  onTagClick,
}: DiscoverHubCardProps) {
  const {
    isUpvoted: liked,
    upvoteCount,
    handleUpvote,
    isLoading,
  } = useDiscoverUpvote({
    hubId: id,
    initialUpvoted: isUpvoted,
    initialCount: _count.upvotes,
  });

  // Activity level configuration with better styling
  const activityConfig = {
    LOW: {
      label: 'Quiet',
      color: 'bg-slate-500',
      textColor: 'text-slate-400',
      bgColor: 'bg-slate-500/10',
      borderColor: 'border-slate-500/20',
    },
    MEDIUM: {
      label: 'Active',
      color: 'bg-blue-500',
      textColor: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20',
    },
    HIGH: {
      label: 'Buzzing',
      color: 'bg-emerald-500',
      textColor: 'text-emerald-400',
      bgColor: 'bg-emerald-500/10',
      borderColor: 'border-emerald-500/20',
    },
  };

  const config = activityConfig[activityLevel] || activityConfig.LOW;

  return (
    <Card className="group relative overflow-hidden premium-card hover:border-purple-500/50 hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 ease-out hover:scale-[1.02] rounded-[var(--radius)] flex flex-col h-full min-h-[420px]">
      {/* Banner Background */}
      {bannerUrl && (
        <div className="absolute inset-0 opacity-20">
          <Image src={bannerUrl} alt="" fill className="object-cover" />
          <div className="absolute inset-0 bg-gradient-to-t from-gray-900 via-gray-900/80 to-transparent" />
        </div>
      )}

      {/* NSFW Badge */}
      {nsfw && (
        <div className="absolute top-4 right-4 z-10 px-2 py-1 bg-red-500/20 backdrop-blur-sm border border-red-500/50 rounded-full text-xs font-medium text-red-400">
          🔞 NSFW
        </div>
      )}

      <CardHeader className="relative pb-4">
        <div className="flex items-start gap-4">
          <Avatar className="h-16 w-16 ring-2 ring-gray-700/50 group-hover:ring-purple-500/30 transition-all duration-300">
            <AvatarImage src={iconUrl || '/default-server.svg'} alt={name} />
            <AvatarFallback className="bg-gray-800 text-white font-semibold">
              {name.substring(0, 2).toUpperCase()}
            </AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-xl font-bold text-white truncate">{name}</CardTitle>
              {verified && (
                <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}
              {partnered && (
                <div className="px-2 py-0.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full text-xs font-medium text-white">
                  Partner
                </div>
              )}
            </div>

            <CardDescription className="text-gray-300 line-clamp-2 leading-relaxed">
              {description || 'A community space for meaningful discussions and connections.'}
            </CardDescription>

            {/* Activity Badge */}
            <div
              className={`inline-flex items-center gap-2 mt-3 px-3 py-1 rounded-full ${config.bgColor} ${config.borderColor} border`}
            >
              <div className={`w-2 h-2 rounded-full ${config.color} animate-pulse`} />
              <span className={`text-sm font-medium ${config.textColor}`}>{config.label}</span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="relative space-y-4 flex-1">
        {/* Tags */}
        <div className="min-h-[32px] flex items-start">
          {tags && tags.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {tags.slice(0, 3).map((tag) => (
                <button
                  key={tag.name}
                  onClick={() => onTagClick?.(tag.name)}
                  className="inline-flex items-center px-3 py-1 rounded-full bg-gray-800/60 border border-gray-700/50 text-xs font-medium text-gray-300 hover:text-white hover:bg-gray-700/60 hover:border-gray-600/50 transition-all duration-200 cursor-pointer focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                >
                  #{tag.name}
                </button>
              ))}
              {tags.length > 3 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full bg-gray-800/40 border border-gray-700/30 text-xs text-gray-400">
                  +{tags.length - 3} more
                </span>
              )}
            </div>
          ) : (
            <div className="h-8" />
          )}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
            <MessageCircle className="h-5 w-5 text-purple-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">
              {weeklyMessageCount?.toLocaleString() ?? '0'}
            </div>
            <div className="text-gray-400 text-xs font-medium">Msgs/w</div>
          </div>

          <div className="text-center p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
            <Users className="h-5 w-5 text-blue-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">
              {_count.connections?.toLocaleString() ?? '0'}
            </div>
            <div className="text-gray-400 text-xs font-medium">Members</div>
          </div>

          <div className="text-center p-3 rounded-lg bg-gray-800/40 border border-gray-700/30">
            <Heart className="h-5 w-5 text-red-400 mx-auto mb-2" />
            <div className="text-white font-bold text-lg">
              {upvoteCount?.toLocaleString() ?? '0'}
            </div>
            <div className="text-gray-400 text-xs font-medium">Upvotes</div>
          </div>
        </div>
      </CardContent>

      <CardFooter className="relative pt-4 mt-auto">
        <div className="flex gap-3 w-full">
          <Link href={`/hubs/${id}`} className="flex-1">
            <Button className="btn-primary w-full group">
              <Eye className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
              Explore Hub
            </Button>
          </Link>

          <Button
            variant="outline"
            size="icon"
            onClick={handleUpvote}
            disabled={isLoading}
            className={`shrink-0 rounded-[var(--radius-button)] border-2 transition-all duration-300 ${
              liked
                ? 'bg-red-500/20 border-red-500/50 text-red-400 hover:bg-red-500/30'
                : 'bg-gray-800/50 border-gray-700/50 text-gray-400 hover:text-white hover:border-gray-600'
            }`}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Heart
                className={cn(
                  'h-4 w-4 transition-all duration-300',
                  liked ? 'fill-red-400 scale-110' : 'hover:scale-110',
                )}
              />
            )}
          </Button>
        </div>
      </CardFooter>

      {/* Subtle gradient overlay on hover */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-600/0 to-indigo-600/0 group-hover:from-purple-600/5 group-hover:to-indigo-600/5 transition-all duration-500 rounded-[var(--radius)] pointer-events-none" />
    </Card>
  );
}
