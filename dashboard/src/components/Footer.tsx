'use client';

import {
  ExternalLink,
  FileText,
  Heart,
  HelpCircle,
  MessageCircle,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { Button } from './ui/button';

/**
 * 7️⃣ Footer
 * Links to terms, privacy, community guidelines, social links, and Discord server invite
 */
export function Footer() {
  const footerLinks = {
    legal: [
      { label: 'Terms of Service', href: '/terms', icon: FileText },
      { label: 'Privacy Policy', href: '/privacy', icon: Shield },
      { label: 'Community Guidelines', href: '/guidelines', icon: Users },
      { label: 'Support', href: '/support', icon: HelpCircle },
    ],
    social: [
      {
        label: 'Discord Server',
        href: '/support',
        icon: MessageCircle,
        description: 'Join InterChat HQ',
      },
      {
        label: 'GitHub',
        href: 'https://github.com/interchatapp',
        icon: ExternalLink,
        description: 'Open Source',
      },
      {
        label: 'Twitter',
        href: 'https://twitter.com/interchatapp',
        icon: ExternalLink,
        description: '@InterChatApp',
      },
    ],
    resources: [
      { label: 'Documentation', href: '/docs', icon: FileText },
      { label: 'API Reference', href: '/docs/api', icon: ExternalLink },
      { label: 'Status Page', href: '/status', icon: Shield },
      { label: 'Changelog', href: '/changelog', icon: FileText },
    ],
  };

  return (
    <footer className="bg-gray-950 border-t border-gray-800/50">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-1"
          >
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <MessageCircle className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white">InterChat</h3>
            </div>
            <p className="text-gray-400 text-sm leading-relaxed mb-6">
              Connecting Discord communities across servers. Build bridges, share experiences, and
              grow your community with InterChat&apos;s cross-server hub system.
            </p>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-400 fill-current" />
              <span>for the Discord community</span>
            </div>
          </motion.div>

          {/* Legal Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4">Legal & Support</h4>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                  >
                    <link.icon className="w-4 h-4" />
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Resources */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4">Resources</h4>
            <ul className="space-y-3">
              {footerLinks.resources.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                  >
                    <link.icon className="w-4 h-4" />
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Social Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <h4 className="text-lg font-semibold text-white mb-4">Community</h4>
            <div className="space-y-4">
              {footerLinks.social.map((link) => (
                <div key={link.href}>
                  <Link
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group"
                  >
                    <div className="flex items-center gap-3 p-3 rounded-lg bg-gray-800/30 hover:bg-gray-800/50 transition-all duration-200 border border-gray-700/50 hover:border-gray-600/50">
                      <div className="w-8 h-8 rounded-lg bg-gray-700/50 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                        <link.icon className="w-4 h-4 text-gray-300 group-hover:text-white" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-white group-hover:text-blue-300 transition-colors">
                          {link.label}
                        </div>
                        <div className="text-xs text-gray-400">{link.description}</div>
                      </div>
                      <ExternalLink className="w-3 h-3 text-gray-500 ml-auto opacity-0 group-hover:opacity-100 transition-opacity" />
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="border-t border-gray-800/50 mt-12 pt-8"
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-500">
              © {new Date().getFullYear()} InterChat. All rights reserved.
            </div>

            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500">Version 5.0.0</div>
              <Button
                className="flex items-center gap-2"
                variant="outline"
                size="sm"
                onClick={() => window.open('https://status.interchat.tech', '_blank')}
              >
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-500">All systems operational</span>
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
