'use client';

import { ReactNode } from 'react';
import { TanstackQueryProvider } from './providers/query-provider';
import { HydrationBoundaryProvider } from './providers/hydration-boundary';
import { TRPCProvider } from './providers/trpc-provider';
import { DehydratedState } from '@/lib/tanstack-query';

export function Providers({
  children,
  dehydratedState = null,
}: {
  children: ReactNode;
  dehydratedState?: DehydratedState | null;
}) {
  return (
    <TanstackQueryProvider>
      <HydrationBoundaryProvider state={dehydratedState}>
        <TRPCProvider>{children}</TRPCProvider>
      </HydrationBoundaryProvider>
    </TanstackQueryProvider>
  );
}
