'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Info, ExternalLink, Check } from 'lucide-react';

interface UpgradePromptProps {
  trigger?: React.ReactNode;
  feature?: string;
  children?: React.ReactNode;
}

export function UpgradePrompt({ trigger, feature = 'this feature', children }: UpgradePromptProps) {
  const [open, setOpen] = useState(false);

  const defaultTrigger = (
    <Button
      variant="outline"
      className="bg-gradient-to-r from-blue-600/20 to-blue-700/20 border-blue-500/30 text-blue-400 hover:from-blue-600/30 hover:to-blue-700/30 cursor-pointer"
    >
      <Info className="w-4 h-4 mr-2" />
      Learn More
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="sm:max-w-md bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-white">
            <Info className="w-5 h-5 text-blue-500" />
            Feature Information
          </DialogTitle>
          <DialogDescription className="text-gray-300">
            {feature} is currently not available.
          </DialogDescription>
        </DialogHeader>

        <Card className="border-gray-800 bg-gray-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-white flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-500" />
              Feature Status
            </CardTitle>
            <CardDescription className="text-gray-400">
              This feature is currently under development
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Check className="w-4 h-4 text-blue-400 flex-shrink-0" />
              Feature is being developed
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Check className="w-4 h-4 text-blue-400 flex-shrink-0" />
              Will be available in future updates
            </div>
          </CardContent>
        </Card>

        {children && (
          <div className="text-sm text-gray-400 bg-gray-800/50 p-3 rounded-lg">{children}</div>
        )}

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            className="border-gray-700 text-gray-300 hover:bg-gray-800"
          >
            Got it
          </Button>
          <Button
            asChild
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-none text-white"
          >
            <a
              href="https://discord.gg/interchat"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2"
            >
              <Info className="w-4 h-4" />
              Join Discord
              <ExternalLink className="w-4 h-4" />
            </a>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface InlineUpgradePromptProps {
  feature?: string;
  className?: string;
}

export function InlineUpgradePrompt({
  feature = 'this feature',
  className,
}: InlineUpgradePromptProps) {
  return (
    <Card
      className={`border-blue-500/30 bg-gradient-to-r from-blue-900/20 to-blue-900/20 shadow-lg shadow-blue-500/5 ${className}`}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <h4 className="font-medium text-blue-400">ℹ️ Feature Information</h4>
            <p className="text-sm text-gray-300">{feature} is currently under development.</p>
            <UpgradePrompt feature={feature}>
              <p>This feature will be available in future updates. Join our Discord for updates!</p>
            </UpgradePrompt>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
