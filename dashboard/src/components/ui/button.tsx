import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-500/30 hover:from-purple-600 hover:to-blue-600 active:scale-95",
        destructive:
          "bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/30 hover:from-red-600 hover:to-red-700 active:scale-95",
        outline:
          "border border-white/20 bg-white/5 backdrop-blur-sm text-white shadow-lg hover:bg-white/10 hover:border-white/30 hover:shadow-xl active:scale-95",
        secondary:
          "bg-gradient-to-r from-gray-700 to-gray-800 text-white shadow-lg shadow-gray-700/25 hover:shadow-xl hover:shadow-gray-700/30 hover:from-gray-600 hover:to-gray-700 active:scale-95",
        ghost: "text-gray-300 hover:text-white hover:bg-white/10 active:scale-95",
        link: "text-purple-400 underline-offset-4 hover:underline hover:text-purple-300",
      },
      size: {
        default: "h-10 px-6 py-2 has-[>svg]:px-5 rounded-[var(--radius-button)]",
        sm: "h-8 rounded-[var(--radius-md)] gap-1.5 px-4 has-[>svg]:px-3",
        lg: "h-12 rounded-[var(--radius-button)] px-8 has-[>svg]:px-6 text-base",
        icon: "size-10 rounded-[var(--radius-button)]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
