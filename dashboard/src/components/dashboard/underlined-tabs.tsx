'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Link from 'next/link';
import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface UnderlinedTabsProps {
  defaultValue: string;
  tabs: {
    value: string;
    label: React.ReactNode;
    color?: 'indigo' | 'blue' | 'green' | 'purple' | 'red' | 'pink' | 'orange';
    icon?: React.ReactNode;
    href?: string;
    badge?: string | number; // New: for notification badges
  }[];
  children?: React.ReactNode;
  className?: string;
  navigational?: boolean;
  compact?: boolean; // New: for smaller tabs
}

export function UnderlinedTabs({
  defaultValue,
  tabs,
  children,
  className = '',
  navigational = false,
  compact = false,
}: UnderlinedTabsProps) {
  const scrollContainer = React.useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = React.useState(false);
  const [canScrollRight, setCanScrollRight] = React.useState(false);

  // Simplified scroll detection
  const updateScrollIndicators = React.useCallback(() => {
    if (!scrollContainer.current) return;

    const { scrollLeft, scrollWidth, clientWidth } = scrollContainer.current;
    setCanScrollLeft(scrollLeft > 5);
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 5);
  }, []);

  // Setup scroll listener with cleanup
  React.useEffect(() => {
    const container = scrollContainer.current;
    if (!container) return;

    updateScrollIndicators();
    container.addEventListener('scroll', updateScrollIndicators, { passive: true });
    window.addEventListener('resize', updateScrollIndicators, { passive: true });

    return () => {
      container.removeEventListener('scroll', updateScrollIndicators);
      window.removeEventListener('resize', updateScrollIndicators);
    };
  }, [updateScrollIndicators]);

  // Smooth scroll functions
  const scrollLeft = () => {
    scrollContainer.current?.scrollBy({ left: -200, behavior: 'smooth' });
  };

  const scrollRight = () => {
    scrollContainer.current?.scrollBy({ left: 200, behavior: 'smooth' });
  };

  // Simplified color system
  const getColorClasses = (color: string = 'indigo') => {
    const colorMap = {
      indigo: 'data-[state=active]:border-indigo-400 data-[state=active]:text-indigo-300',
      blue: 'data-[state=active]:border-blue-400 data-[state=active]:text-blue-300',
      green: 'data-[state=active]:border-emerald-400 data-[state=active]:text-emerald-300',
      purple: 'data-[state=active]:border-purple-400 data-[state=active]:text-purple-300',
      red: 'data-[state=active]:border-red-400 data-[state=active]:text-red-300',
      pink: 'data-[state=active]:border-pink-400 data-[state=active]:text-pink-300',
      orange: 'data-[state=active]:border-amber-400 data-[state=active]:text-amber-300',
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.indigo;
  };

  return (
    <div className={`w-full ${className}`}>
      <Tabs defaultValue={defaultValue} className="w-full">
        {/* Tab Navigation */}
        <div className="relative border-b border-gray-700/50 backdrop-blur-sm">
          {/* Scroll Buttons - Desktop Only */}
          {canScrollLeft && (
            <button
              onClick={scrollLeft}
              className="hidden sm:flex absolute left-2 top-1/2 -translate-y-1/2 z-20 items-center justify-center w-8 h-8 bg-gray-800/80 hover:bg-gray-700/80 border border-gray-600/50 rounded-lg transition-all duration-200"
              aria-label="Scroll tabs left"
            >
              <ChevronLeft className="h-4 w-4 text-gray-300" />
            </button>
          )}

          {canScrollRight && (
            <button
              onClick={scrollRight}
              className="hidden sm:flex absolute right-2 top-1/2 -translate-y-1/2 z-20 items-center justify-center w-8 h-8 bg-gray-800/80 hover:bg-gray-700/80 border border-gray-600/50 rounded-lg transition-all duration-200"
              aria-label="Scroll tabs right"
            >
              <ChevronRight className="h-4 w-4 text-gray-300" />
            </button>
          )}

          {/* Mobile Scroll Indicators */}
          {canScrollLeft && (
            <div className="sm:hidden absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-gray-900/90 to-transparent pointer-events-none z-10" />
          )}
          {canScrollRight && (
            <div className="sm:hidden absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-gray-900/90 to-transparent pointer-events-none z-10" />
          )}

          {/* Scrollable Tabs Container */}
          <div
            ref={scrollContainer}
            className="overflow-x-auto scrollbar-hide px-4 sm:px-12"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            <TabsList className="flex items-center gap-1 bg-transparent h-auto p-0 w-max min-w-full sm:min-w-0 sm:mx-auto sm:justify-center">
              {tabs.map((tab) => {
                const colorClasses = getColorClasses(tab.color);
                const isCompact = compact;

                const TabContent = (
                  <div className={`flex items-center gap-2 ${isCompact ? 'gap-1.5' : 'gap-2'}`}>
                    {tab.icon && (
                      <span className={`flex-shrink-0 ${isCompact ? 'text-sm' : ''}`}>
                        {tab.icon}
                      </span>
                    )}
                    <span className={`whitespace-nowrap font-medium ${isCompact ? 'text-sm' : ''}`}>
                      {tab.label}
                    </span>
                    {tab.badge && (
                      <span className="flex-shrink-0 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[18px] h-[18px] flex items-center justify-center">
                        {tab.badge}
                      </span>
                    )}
                  </div>
                );

                const triggerClasses = `
                relative px-4 py-3 text-gray-400 hover:text-gray-200 
                border-b-2 border-transparent transition-all duration-200
                bg-transparent shadow-none focus:outline-none focus:ring-0
                data-[state=active]:bg-transparent rounded-none
                hover:bg-gray-800/30 hover:rounded-lg cursor-pointer
                ${colorClasses}
                ${isCompact ? 'px-3 py-2' : 'px-4 py-3'}
              `.trim();

                const TabTrigger = (
                  <TabsTrigger key={tab.value} value={tab.value} className={triggerClasses}>
                    {TabContent}
                  </TabsTrigger>
                );

                // Wrap with Link if navigational
                return navigational && tab.href ? (
                  <Link key={tab.value} href={tab.href} className="flex">
                    {TabTrigger}
                  </Link>
                ) : (
                  TabTrigger
                );
              })}
            </TabsList>
          </div>
        </div>

        {/* Tab Content */}
        {!navigational && children}
      </Tabs>
    </div>
  );
}
