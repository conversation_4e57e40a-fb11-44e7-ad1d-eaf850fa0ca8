'use client';;
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { Connection, Hub } from '@/lib/generated/prisma/client';
import { formatDistanceToNow } from 'date-fns';
import { ExternalLink, MoreHorizontal, Power, PowerOff, Settings, Trash2 } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useTRPC } from '@/utils/trpc';

import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";

interface ServerConnectionsTableProps {
  connections: (Connection & { hub: Hub })[];
}

export function ServerConnectionsTable({ connections }: ServerConnectionsTableProps) {
  const trpc = useTRPC();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // tRPC mutations for connection management
  const updateConnectionMutation = useMutation(trpc.connection.update.mutationOptions({
    onSuccess: (data, variables) => {
      toast({
        title: variables.connected ? 'Connection enabled' : 'Connection disabled',
        description: variables.connected
          ? 'The connection has been enabled successfully.'
          : 'The connection has been disabled successfully.',
      });
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries(trpc.connection.pathFilter());
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update connection',
        variant: 'destructive',
      });
    },
  }));

  const deleteConnectionMutation = useMutation(trpc.connection.remove.mutationOptions({
    onSuccess: () => {
      toast({
        title: 'Connection deleted',
        description: 'The connection has been deleted successfully.',
      });
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries(trpc.connection.pathFilter());
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete connection',
        variant: 'destructive',
      });
    },
  }));

  if (connections.length === 0) {
    return (
      <div className="text-center py-10">
        <h3 className="text-xl font-semibold mb-2">No Connections</h3>
        <p className="text-gray-400 mb-4">No connections found for this server.</p>
      </div>
    );
  }

  // Function to toggle connection status using tRPC
  const toggleConnection = (connectionId: string, connected: boolean) => {
    updateConnectionMutation.mutate({
      connectionId,
      connected: !connected,
    });
  };

  // Function to delete a connection using tRPC
  const deleteConnection = (connectionId: string) => {
    deleteConnectionMutation.mutate({
      connectionId,
    });
  };

  const isLoading = updateConnectionMutation.isPending || deleteConnectionMutation.isPending;

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Hub</TableHead>
            <TableHead>Channel ID</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Connected Since</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {connections.map((connection) => {
            const formattedDate = new Date(connection.createdAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            });

            const timeAgo = formatDistanceToNow(new Date(connection.createdAt), {
              addSuffix: true,
            });

            return (
              <TableRow key={connection.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="h-6 w-6 rounded-full border border-gray-700/50 overflow-hidden">
                      <Image
                        src={connection.hub.iconUrl}
                        alt={connection.hub.name}
                        width={24}
                        height={24}
                        className="object-cover"
                        style={{ width: '100%', height: '100%' }}
                      />
                    </div>
                    <span className="font-medium">{connection.hub.name}</span>
                  </div>
                </TableCell>
                <TableCell className="font-mono text-xs">{connection.channelId}</TableCell>
                <TableCell>
                  {connection.connected ? (
                    <Badge
                      variant="outline"
                      className="bg-green-500/10 text-green-400 border-green-500/20"
                    >
                      Connected
                    </Badge>
                  ) : (
                    <Badge
                      variant="outline"
                      className="bg-red-500/10 text-red-400 border-red-500/20"
                    >
                      Paused
                    </Badge>
                  )}
                </TableCell>
                <TableCell title={formattedDate}>{timeAgo}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 hover:bg-gray-800/50"
                        disabled={isLoading}
                      >
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50"
                    >
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator className="bg-gray-800/50" />
                      <DropdownMenuItem className="cursor-pointer hover:bg-gray-800/50">
                        <Link
                          href={`/hubs/${connection.hubId}`}
                          target="_blank"
                          className="flex items-center w-full"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Hub
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer hover:bg-gray-800/50">
                        <Link
                          href={`/dashboard/connections/${connection.id}/edit`}
                          className="flex items-center w-full"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Edit Connection
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator className="bg-gray-800/50" />
                      <DropdownMenuItem
                        className={
                          connection.connected
                            ? 'text-red-400 cursor-pointer hover:bg-red-900/30 hover:text-red-300'
                            : 'text-green-400 cursor-pointer hover:bg-green-900/30 hover:text-green-300'
                        }
                        onClick={() => toggleConnection(connection.id, connection.connected)}
                        disabled={isLoading}
                      >
                        {connection.connected ? (
                          <>
                            <PowerOff className="h-4 w-4 mr-2" />
                            Pause Connection
                          </>
                        ) : (
                          <>
                            <Power className="h-4 w-4 mr-2" />
                            Unpause Connection
                          </>
                        )}
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-400 cursor-pointer hover:bg-red-900/30 hover:text-red-300"
                        onClick={() => {
                          if (
                            confirm(
                              'Are you sure you want to delete this connection? This action cannot be undone.',
                            )
                          ) {
                            deleteConnection(connection.id);
                          }
                        }}
                        disabled={isLoading}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Connection
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
