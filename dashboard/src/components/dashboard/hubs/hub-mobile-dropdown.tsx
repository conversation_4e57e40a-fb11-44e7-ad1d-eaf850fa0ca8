'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Bell,
  ChevronDown,
  FileText,
  Gavel,
  Globe,
  Home,
  Menu,
  MessageSquare,
  MessageCircle,
  Package,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HubMobileDropdownProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
}

interface DropdownNavItemProps {
  href: string;
  icon: React.ElementType;
  label: string;
  active: boolean;
}

function DropdownNavItem({ href, icon: Icon, label, active }: DropdownNavItemProps) {
  return (
    <DropdownMenuItem asChild>
      <Link
        href={href}
        className={cn(
          'flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-300 cursor-pointer group relative border',
          'hover:bg-gray-800/60 focus:bg-gray-800/60',
          active
            ? 'bg-gradient-to-r from-purple-500/15 to-indigo-500/15 text-purple-300 border-purple-400/40 shadow-lg shadow-purple-500/5 font-medium'
            : 'text-gray-300 hover:text-white border-transparent hover:border-gray-700/50',
        )}
      >
        <div
          className={cn(
            'p-1.5 rounded-xl transition-all duration-300 shrink-0',
            active
              ? 'text-purple-300 bg-purple-400/20 shadow-sm shadow-purple-500/20'
              : 'text-gray-400 group-hover:text-white group-hover:bg-purple-400/15',
          )}
        >
          <Icon className="h-4 w-4" />
        </div>
        <span className="text-sm truncate">{label}</span>
      </Link>
    </DropdownMenuItem>
  );
}

export function HubMobileDropdown({
  hubId,
  canModerate = false,
  canEdit = false,
}: HubMobileDropdownProps) {
  const pathname = usePathname();

  // Define navigation structure exactly matching hub sidebar
  const navigationSections = [
    {
      key: 'main',
      title: null,
      items: [
        {
          value: 'overview',
          label: 'Overview',
          color: 'default' as const,
          icon: MessageSquare,
          href: `/dashboard/hubs/${hubId}`,
          show: true,
        },
        {
          value: 'discovery',
          label: 'Discovery',
          color: 'yellow' as const,
          icon: Globe,
          href: `/dashboard/hubs/${hubId}/discoverability`,
          show: canEdit,
        },
      ],
    },
    {
      key: 'management',
      title: 'Management',
      items: [
        {
          value: 'members',
          label: 'Team',
          color: 'blue' as const,
          icon: Users,
          href: `/dashboard/hubs/${hubId}/members`,
          show: canModerate,
        },
        {
          value: 'connections',
          label: 'Connections',
          color: 'green' as const,
          icon: Home,
          href: `/dashboard/hubs/${hubId}/connections`,
          show: canModerate,
        },
        {
          value: 'messages',
          label: 'Messages',
          color: 'purple' as const,
          icon: MessageCircle,
          href: `/dashboard/hubs/${hubId}/messages`,
          show: canModerate,
        },
        {
          value: 'logging',
          label: 'Logging',
          color: 'purple' as const,
          icon: FileText,
          href: `/dashboard/hubs/${hubId}/logging`,
          show: canEdit,
        },
        {
          value: 'modules',
          label: 'Modules',
          color: 'default' as const,
          icon: Package,
          href: `/dashboard/hubs/${hubId}/modules`,
          show: true,
        },
      ],
    },
    {
      key: 'moderation',
      title: 'Moderation',
      items: [
        {
          value: 'reports',
          label: 'Reports',
          color: 'red' as const,
          icon: Shield,
          href: `/dashboard/hubs/${hubId}/reports`,
          show: canModerate,
        },
        {
          value: 'appeals',
          label: 'Appeals',
          color: 'orange' as const,
          icon: Bell,
          href: `/dashboard/hubs/${hubId}/appeals`,
          show: canModerate,
        },
        {
          value: 'infractions',
          label: 'Infractions',
          color: 'orange' as const,
          icon: Gavel,
          href: `/dashboard/hubs/${hubId}/infractions`,
          show: canModerate,
        },
        {
          value: 'anti-swear',
          label: 'Anti-Swear',
          color: 'red' as const,
          icon: AlertTriangle,
          href: `/dashboard/hubs/${hubId}/anti-swear`,
          show: canModerate,
        },
      ],
    },
  ];

  // Filter sections based on permissions
  const visibleSections = navigationSections
    .map((section) => ({
      ...section,
      items: section.items.filter((item) => item.show),
    }))
    .filter((section) => section.items.length > 0);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          <Button className="w-full justify-between gap-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 border-0 text-white font-medium py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group relative overflow-hidden">
            {/* Background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            <div className="flex items-center gap-3 relative z-10">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Menu className="h-5 w-5" />
              </motion.div>
              <span className="text-base">Hub Navigation</span>
            </div>

            <ChevronDown className="h-5 w-5 text-white group-hover:text-white/70 transition-colors duration-300" />
          </Button>
        </motion.div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="center"
        className="w-80 max-h-[70vh] overflow-y-auto bg-gray-900/95 backdrop-blur-md border-gray-800/50 text-gray-100 shadow-2xl rounded-xl p-2"
        sideOffset={8}
      >
        <DropdownMenuLabel className="px-3 py-2 text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
          Hub Dashboard
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-800/50 my-2" />

        {visibleSections.map((section, sectionIndex) => (
          <div key={section.key}>
            {sectionIndex > 0 && <DropdownMenuSeparator className="bg-gray-800/30 my-2" />}

            <DropdownMenuGroup>
              {section.title && (
                <DropdownMenuLabel className="px-3 py-1 text-xs font-medium text-gray-400 uppercase tracking-wider flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-full opacity-60" />
                  <span className="bg-gradient-to-r from-gray-300 to-gray-400 bg-clip-text text-transparent">
                    {section.title}
                  </span>
                </DropdownMenuLabel>
              )}
              {section.items.map((item) => (
                <DropdownNavItem
                  key={item.value}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  active={pathname === item.href}
                />
              ))}
            </DropdownMenuGroup>
          </div>
        ))}

        <DropdownMenuSeparator className="bg-gray-800/30 my-2" />
        <div className="px-3 py-2">
          <p className="text-xs text-gray-500 text-center mt-1 flex items-center justify-center gap-1">
            <span>💡</span> Quick access to all hub features
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
