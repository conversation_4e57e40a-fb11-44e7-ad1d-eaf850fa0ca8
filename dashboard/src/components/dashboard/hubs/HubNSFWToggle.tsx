'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { useUpdateHub } from '@/hooks/use-hub-settings';
import { AlertTriangle, Save, Shield } from 'lucide-react';
import { useState } from 'react';

interface HubNSFWToggleProps {
  hubId: string;
  currentNsfw: boolean;
}

export function HubNSFWToggle({ hubId, currentNsfw }: HubNSFWToggleProps) {
  const { toast } = useToast();
  const [isNsfw, setIsNsfw] = useState<boolean>(currentNsfw);
  const [hasChanges, setHasChanges] = useState(false);

  const updateHubMutation = useUpdateHub(hubId);

  const handleNsfwChange = (checked: boolean) => {
    setIsNsfw(checked);
    setHasChanges(checked !== currentNsfw);
  };

  const handleSaveNsfw = async () => {
    if (!hasChanges) return;

    try {
      await updateHubMutation.mutateAsync({
        hubId,
        nsfw: isNsfw,
      });

      setHasChanges(false);
      toast({
        title: 'NSFW Setting Updated',
        description: `Hub has been marked as ${isNsfw ? 'NSFW' : 'SFW'}.`,
      });
    } catch (error) {
      console.error('Error updating NSFW setting:', error);
      toast({
        title: 'Error',
        description: 'Failed to update NSFW setting. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleResetNsfw = () => {
    setIsNsfw(currentNsfw);
    setHasChanges(false);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <Shield className="w-5 h-5 text-orange-400" />
        <div>
          <Label htmlFor="nsfw-toggle" className="text-base font-medium text-gray-200">
            NSFW Content
          </Label>
          <p className="text-sm text-gray-400 mt-1">
            Mark this hub as containing Not Safe For Work content
          </p>
        </div>
      </div>

      <div className="flex items-center justify-between p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
        <div className="flex items-center gap-3">
          <Switch
            id="nsfw-toggle"
            checked={isNsfw}
            onCheckedChange={handleNsfwChange}
            className="data-[state=checked]:bg-orange-500"
          />
          <div>
            <span className="text-sm font-medium text-gray-200">NSFW Hub</span>
            <p className="text-xs text-gray-400">
              This hub contains adult content and will be filtered from search results for users who
              haven&apos;t opted in
            </p>
          </div>
        </div>

        {hasChanges && (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetNsfw}
              className="text-gray-400 hover:text-white hover:bg-gray-700/50"
            >
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSaveNsfw}
              disabled={updateHubMutation.isPending}
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              {updateHubMutation.isPending ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  Save Changes
                </div>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Warning message for NSFW content */}
      {isNsfw && (
        <div className="flex items-start gap-3 p-4 bg-orange-500/10 border border-orange-500/20 rounded-lg">
          <AlertTriangle className="w-5 h-5 text-orange-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-orange-200 space-y-2">
            <p>
              <strong>Important NSFW Guidelines:</strong>
            </p>
            <ul className="list-disc list-inside space-y-1 ml-2 text-orange-300/80">
              <li>Only NSFW Discord channels can connect to NSFW hubs</li>
              <li>
                NSFW hubs are hidden from users who haven&apos;t opted in to see adult content
              </li>
              <li>
                Ensure all content shared in this hub complies with Discord&apos;s Terms of Service
              </li>
              <li>Moderators should actively enforce appropriate content guidelines</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}
