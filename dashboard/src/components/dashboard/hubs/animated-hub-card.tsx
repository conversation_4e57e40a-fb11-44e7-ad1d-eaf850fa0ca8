"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PermissionLevel } from "@/lib/constants";
import { formatDistanceToNow } from "date-fns";
import { motion } from "motion/react";
import { Clock, Edit3, Globe, Home, Lock, MessageSquare, Users } from "lucide-react";
import Link from "next/link";
import { useInView } from "react-intersection-observer";
import { useState } from "react";

interface HubWithPermission {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  connections: { id: string }[];
  upvotes: { id: string }[];
  lastActive: Date | null;
  private: boolean;
  permissionLevel: PermissionLevel;
}

interface AnimatedHubCardProps {
  hub: HubWithPermission;
  index: number;
}

export function AnimatedHubCard({ hub, index }: AnimatedHubCardProps) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [isHovering, setIsHovering] = useState(false);

  const lastActive = hub.lastActive
    ? formatDistanceToNow(new Date(hub.lastActive), { addSuffix: true })
    : "Never";

  // Determine card color based on permission level
  const getCardColor = () => {
    switch (hub.permissionLevel) {
      case PermissionLevel.OWNER:
        return "from-purple-500/10 to-purple-900/5 border-purple-500/20 hover:border-purple-500/40";
      case PermissionLevel.MANAGER:
        return "from-blue-500/10 to-blue-900/5 border-blue-500/20 hover:border-blue-500/40";
      case PermissionLevel.MODERATOR:
        return "from-indigo-500/10 to-indigo-900/5 border-indigo-500/20 hover:border-indigo-500/40";
      default:
        return "from-gray-500/10 to-gray-900/5 border-gray-500/20 hover:border-gray-500/40";
    }
  };

  // Determine role badge based on permission level
  const getRoleBadge = () => {
    switch (hub.permissionLevel) {
      case PermissionLevel.OWNER:
        return (
          <span className="px-3 py-1 text-xs font-medium rounded-[var(--radius-badge)] bg-gradient-to-r from-purple-500/20 to-purple-600/20 text-purple-300 border border-purple-500/30 shadow-lg">
            Owner
          </span>
        );
      case PermissionLevel.MANAGER:
        return (
          <span className="px-3 py-1 text-xs font-medium rounded-[var(--radius-badge)] bg-gradient-to-r from-blue-500/20 to-blue-600/20 text-blue-300 border border-blue-500/30 shadow-lg">
            Manager
          </span>
        );
      case PermissionLevel.MODERATOR:
        return (
          <span className="px-3 py-1 text-xs font-medium rounded-[var(--radius-badge)] bg-gradient-to-r from-indigo-500/20 to-indigo-600/20 text-indigo-300 border border-indigo-500/30 shadow-lg">
            Moderator
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      whileHover={{ y: -8, scale: 1.02, transition: { duration: 0.2, type: "spring" } }}
      className="h-full"
    >
      <Card
        className={`border bg-gradient-to-br ${getCardColor()} transition-all duration-200 overflow-hidden flex flex-col h-full min-h-[320px] group hover:shadow-xl hover:shadow-purple-500/10`}
      >
        <CardHeader className="pb-3 relative">
          <div className="absolute top-4 right-4 z-10">{getRoleBadge()}</div>
          <div className="flex items-center gap-3 relative z-10">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.2, type: "spring" }}
              className="relative"
              onMouseEnter={() => setIsHovering(true)}
              onMouseLeave={() => setIsHovering(false)}
            >
              <Avatar className="h-16 w-16 border-2 border-gray-700/50 group-hover:border-purple-500/50 transition-all duration-200 flex-shrink-0 ring-2 ring-transparent group-hover:ring-purple-500/20">
                <AvatarImage
                  src={
                    hub.iconUrl?.includes(".gif")
                      ? hub.iconUrl
                      : hub.iconUrl || "/images/default-hub.png"
                  }
                  alt={hub.name}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-br from-purple-500/20 to-blue-500/20 text-purple-300 font-bold text-lg">
                  {hub.name.substring(0, 2)}
                </AvatarFallback>
              </Avatar>

              {/* Privacy indicator */}
              <div className="absolute -bottom-1 -right-1 w-6 h-6 rounded-[var(--radius-avatar)] bg-gray-900 border-2 border-gray-700 flex items-center justify-center">
                {hub.private ? (
                  <Lock className="h-3 w-3 text-orange-400" />
                ) : (
                  <Globe className="h-3 w-3 text-green-400" />
                )}
              </div>

              {/* Edit Overlay */}
              {isHovering && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="absolute inset-0 bg-black/60 rounded-2xl flex items-center justify-center cursor-pointer"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    window.location.href = `/dashboard/hubs/${hub.id}`;
                  }}
                >
                  <Edit3 className="w-4 h-4 text-white" />
                </motion.div>
              )}
            </motion.div>
            <div className="flex-1 min-w-0 pt-1">
              <CardTitle className="text-lg font-bold text-white group-hover:text-purple-100 transition-colors truncate">
                {hub.name}
              </CardTitle>
              <CardDescription className="line-clamp-2 text-sm text-gray-400 group-hover:text-purple-200 transition-colors mt-1">
                {hub.description || "No description available"}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="py-4 flex-grow">
          <div className="space-y-3 text-sm">
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                  {hub.private ? (
                    <Lock className="h-3 w-3 text-purple-400" />
                  ) : (
                    <Globe className="h-3 w-3 text-green-400" />
                  )}
                </div>
                <span>Privacy</span>
              </span>
              <span className="flex items-center text-gray-200 font-medium">
                {hub.private ? "Private" : "Public"}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                  <Home className="h-3 w-3 text-blue-400" />
                </div>
                <span>Servers</span>
              </span>
              <span className="text-gray-200 font-medium">{hub.connections.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                  <Users className="h-3 w-3 text-purple-400" />
                </div>
                <span>Upvotes</span>
              </span>
              <span className="text-gray-200 font-medium">{hub.upvotes.length}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400 flex items-center gap-2">
                <div className="h-6 w-6 rounded-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 flex items-center justify-center">
                  <Clock className="h-3 w-3 text-indigo-400" />
                </div>
                <span>Last Active</span>
              </span>
              <span className="text-gray-200 font-medium text-right truncate max-w-[120px]">{lastActive}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-0 pb-4 border-t border-gray-800/50">
          <Button
            asChild
            className="mt-3 w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 border-none text-white font-medium rounded-xl shadow-lg hover:shadow-purple-500/25 transition-all duration-300"
          >
            <Link href={`/dashboard/hubs/${hub.id}`}>
              <MessageSquare className="h-4 w-4 mr-2" />
              Manage Hub
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </motion.div>
  );
}
