'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useUpdateHub } from '@/hooks/use-hub-settings';
import { LANGUAGES } from '@/app/hubs/constants';
import { Globe, Save, Check } from 'lucide-react';

interface HubLanguageManagementProps {
  hubId: string;
  currentLanguage?: string;
}

export function HubLanguageManagement({ hubId, currentLanguage }: HubLanguageManagementProps) {
  const { toast } = useToast();
  const [selectedLanguage, setSelectedLanguage] = useState<string>(currentLanguage || 'en');
  const [hasChanges, setHasChanges] = useState(false);

  const updateHubMutation = useUpdateHub(hubId);

  const handleLanguageChange = (value: string) => {
    setSelectedLanguage(value);
    setHasChanges(value !== currentLanguage);
  };

  const handleSaveLanguage = async () => {
    if (!hasChanges) return;

    try {
      await updateHubMutation.mutateAsync({
        hubId,
        language: selectedLanguage,
      });

      setHasChanges(false);
      toast({
        title: 'Language Updated',
        description: 'Hub language has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating language:', error);
      toast({
        title: 'Error',
        description: 'Failed to update hub language. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleResetLanguage = () => {
    setSelectedLanguage(currentLanguage || 'en');
    setHasChanges(false);
  };

  // Get the current language name for display
  const getCurrentLanguageName = () => {
    const language = LANGUAGES.find((lang) => lang.code === (currentLanguage || 'en'));
    return language?.name || 'English';
  };

  const getSelectedLanguageName = () => {
    const language = LANGUAGES.find((lang) => lang.code === selectedLanguage);
    return language?.name || 'English';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center">
          <Globe className="w-5 h-5 text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Hub Language</h3>
          <p className="text-sm text-gray-400">
            Set the primary language for your hub to help users find it
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="language-select" className="text-sm font-medium">
            Primary Language
          </Label>
          <Select value={selectedLanguage} onValueChange={handleLanguageChange}>
            <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70 focus:ring-2 focus:ring-blue-500/50">
              <SelectValue placeholder="Select a language">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-gray-400" />
                  <span>{getSelectedLanguageName()}</span>
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="bg-gray-900 border-gray-700 max-h-[300px]">
              {LANGUAGES.map((language) => (
                <SelectItem
                  key={language.code}
                  value={language.code}
                  className="hover:bg-gray-800 focus:bg-gray-800 cursor-pointer"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-400 font-mono w-8">
                      {language.code.toUpperCase()}
                    </span>
                    <span>{language.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs text-gray-500">
            Current language: <span className="text-gray-400">{getCurrentLanguageName()}</span>
          </p>
        </div>

        {hasChanges && (
          <div className="flex items-center gap-3 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <div className="flex-1">
              <p className="text-sm text-blue-400 font-medium">Language Change Pending</p>
              <p className="text-xs text-gray-400">
                Save your changes to update the hub language to {getSelectedLanguageName()}
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetLanguage}
                className="text-gray-400 hover:text-white hover:bg-gray-800"
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={handleSaveLanguage}
                disabled={updateHubMutation.isPending}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                {updateHubMutation.isPending ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Saving...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Save className="w-4 h-4" />
                    Save Language
                  </div>
                )}
              </Button>
            </div>
          </div>
        )}

        {!hasChanges && currentLanguage && (
          <div className="flex items-center gap-2 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <Check className="w-4 h-4 text-green-400" />
            <p className="text-sm text-green-400">
              Hub language is set to {getCurrentLanguageName()}
            </p>
          </div>
        )}
      </div>

      <div className="text-xs text-gray-500 space-y-1">
        <p>
          <strong>Note:</strong> Setting the correct language helps users discover your hub through
          language filters.
        </p>
        <p>This setting affects how your hub appears in search results and recommendations.</p>
      </div>
    </div>
  );
}
